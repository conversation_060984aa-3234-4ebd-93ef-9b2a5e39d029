#!/usr/bin/env python3
"""
ملف تشغيل سريع لتطبيق PDF Editor Pro
Quick launcher for PDF Editor Pro
"""

import sys
import os
from pathlib import Path

# إضافة مسار المشروع
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def check_python_version():
    """التحقق من إصدار Python"""
    if sys.version_info < (3, 7):
        print("❌ خطأ: يتطلب Python 3.7 أو أحدث")
        print("❌ Error: Python 3.7 or newer is required")
        print(f"الإصدار الحالي / Current version: {sys.version}")
        return False
    return True

def check_dependencies():
    """التحقق من المكتبات المطلوبة"""
    print("🔍 فحص المكتبات المطلوبة...")
    print("🔍 Checking required dependencies...")
    
    required_modules = [
        ('PyQt5', 'PyQt5'),
        ('qtawesome', 'qtawesome'),
        ('fitz', 'PyMuPDF'),
        ('PyPDF2', 'PyPDF2'),
        ('reportlab', 'reportlab'),
        ('PIL', 'Pillow'),
        ('docx2pdf', 'docx2pdf'),
        ('pdf2docx', 'pdf2docx'),
        ('openpyxl', 'openpyxl'),
        ('pptx', 'python-pptx'),
        ('img2pdf', 'img2pdf'),
        ('pdf2image', 'pdf2image'),
        ('pikepdf', 'pikepdf')
    ]
    
    missing_modules = []
    
    for module_name, package_name in required_modules:
        try:
            __import__(module_name)
            print(f"✅ {package_name}")
        except ImportError:
            print(f"❌ {package_name}")
            missing_modules.append(package_name)
    
    if missing_modules:
        print("\n❌ المكتبات التالية مفقودة:")
        print("❌ The following packages are missing:")
        for module in missing_modules:
            print(f"   - {module}")
        
        print("\n📦 لتثبيت المكتبات المطلوبة:")
        print("📦 To install required packages:")
        print("pip install -r requirements.txt")
        print("\nأو تثبيت كل مكتبة على حدة:")
        print("Or install each package individually:")
        for module in missing_modules:
            print(f"pip install {module}")
        
        return False
    
    print("\n✅ جميع المكتبات متوفرة!")
    print("✅ All dependencies are available!")
    return True

def create_directories():
    """إنشاء المجلدات المطلوبة"""
    directories = [
        "resources",
        "resources/icons",
        "resources/styles", 
        "resources/translations",
        "temp",
        "output"
    ]
    
    for directory in directories:
        dir_path = project_root / directory
        dir_path.mkdir(parents=True, exist_ok=True)

def main():
    """الدالة الرئيسية"""
    print("🚀 PDF Editor Pro - تطبيق تحرير ملفات PDF")
    print("🚀 PDF Editor Pro - PDF Editing Application")
    print("=" * 50)
    
    # فحص إصدار Python
    if not check_python_version():
        input("اضغط Enter للخروج / Press Enter to exit...")
        return
    
    # فحص المكتبات
    if not check_dependencies():
        input("اضغط Enter للخروج / Press Enter to exit...")
        return
    
    # إنشاء المجلدات
    create_directories()
    
    print("\n🎯 بدء تشغيل التطبيق...")
    print("🎯 Starting application...")
    
    try:
        # استيراد وتشغيل التطبيق
        from main import main as app_main
        app_main()
        
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف التطبيق بواسطة المستخدم")
        print("⏹️ Application stopped by user")
        
    except Exception as e:
        print(f"\n❌ خطأ في تشغيل التطبيق:")
        print(f"❌ Error running application:")
        print(f"   {str(e)}")
        
        print(f"\n🔧 تفاصيل الخطأ:")
        print(f"🔧 Error details:")
        import traceback
        traceback.print_exc()
        
        input("اضغط Enter للخروج / Press Enter to exit...")

if __name__ == "__main__":
    main()
