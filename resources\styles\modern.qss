/* PDF Editor Pro - Modern Theme Stylesheet */
/* ملف الأنماط للثيم العصري */

/* النافذة الرئيسية */
QMainWindow {
    background-color: #FAFAFA;
    color: #212121;
    font-family: 'Segoe UI', Arial, sans-serif;
    font-size: 11px;
}

/* الشريط الجانبي */
QFrame#sidebar {
    background-color: #FFFFFF;
    border-right: 1px solid #E0E0E0;
    border-radius: 0px;
}

/* أزرار الشريط الجانبي */
QPushButton#sidebarButton {
    text-align: left;
    padding: 12px 16px;
    border: none;
    border-radius: 8px;
    background-color: transparent;
    color: #666666;
    font-size: 12px;
    font-weight: 500;
    margin: 2px 4px;
}

QPushButton#sidebarButton:hover {
    background-color: #E3F2FD;
    color: #2196F3;
}

QPushButton#sidebarButton:pressed {
    background-color: #BBDEFB;
}

QPushButton#sidebarButton:checked {
    background-color: #2196F3;
    color: white;
    font-weight: 600;
}

/* الأزرار العادية */
QPushButton {
    background-color: #2196F3;
    color: white;
    border: none;
    border-radius: 8px;
    padding: 10px 20px;
    font-size: 11px;
    font-weight: bold;
    min-height: 16px;
}

QPushButton:hover {
    background-color: #1976D2;
}

QPushButton:pressed {
    background-color: #1565C0;
}

QPushButton:disabled {
    background-color: #BDBDBD;
    color: #FFFFFF;
}

/* أزرار ثانوية */
QPushButton#secondary {
    background-color: #FFFFFF;
    color: #2196F3;
    border: 1px solid #2196F3;
}

QPushButton#secondary:hover {
    background-color: #E3F2FD;
}

QPushButton#secondary:pressed {
    background-color: #BBDEFB;
}

/* حقول الإدخال */
QLineEdit {
    background-color: #FFFFFF;
    border: 1px solid #E0E0E0;
    border-radius: 6px;
    padding: 8px 12px;
    font-size: 11px;
    color: #212121;
    selection-background-color: #2196F3;
}

QLineEdit:focus {
    border-color: #2196F3;
    border-width: 2px;
}

QLineEdit:disabled {
    background-color: #F5F5F5;
    color: #BDBDBD;
}

/* مناطق النص */
QTextEdit {
    background-color: #FFFFFF;
    border: 1px solid #E0E0E0;
    border-radius: 6px;
    padding: 8px;
    font-size: 11px;
    color: #212121;
    selection-background-color: #2196F3;
}

QTextEdit:focus {
    border-color: #2196F3;
    border-width: 2px;
}

/* القوائم */
QListWidget {
    background-color: #FFFFFF;
    border: 1px solid #E0E0E0;
    border-radius: 6px;
    alternate-background-color: #F5F5F5;
    outline: none;
}

QListWidget::item {
    padding: 8px 12px;
    border-bottom: 1px solid #EEEEEE;
    color: #212121;
}

QListWidget::item:hover {
    background-color: #F5F5F5;
}

QListWidget::item:selected {
    background-color: #E3F2FD;
    color: #2196F3;
    border-left: 3px solid #2196F3;
}

/* الجداول */
QTableWidget {
    background-color: #FFFFFF;
    border: 1px solid #E0E0E0;
    border-radius: 6px;
    gridline-color: #EEEEEE;
    selection-background-color: #E3F2FD;
}

QTableWidget::item {
    padding: 8px;
    border-bottom: 1px solid #EEEEEE;
}

QTableWidget::item:selected {
    background-color: #E3F2FD;
    color: #2196F3;
}

QHeaderView::section {
    background-color: #F5F5F5;
    color: #666666;
    padding: 8px;
    border: none;
    border-bottom: 1px solid #E0E0E0;
    font-weight: 600;
}

/* شريط التقدم */
QProgressBar {
    border: 1px solid #E0E0E0;
    border-radius: 6px;
    text-align: center;
    background-color: #F5F5F5;
    color: #666666;
    font-weight: 600;
}

QProgressBar::chunk {
    background-color: #2196F3;
    border-radius: 5px;
    margin: 1px;
}

/* التبويبات */
QTabWidget::pane {
    border: 1px solid #E0E0E0;
    background-color: #FFFFFF;
    border-radius: 6px;
}

QTabBar::tab {
    background-color: #F5F5F5;
    color: #666666;
    padding: 8px 16px;
    margin-right: 2px;
    border-top-left-radius: 6px;
    border-top-right-radius: 6px;
    border: 1px solid #E0E0E0;
    border-bottom: none;
}

QTabBar::tab:selected {
    background-color: #FFFFFF;
    color: #2196F3;
    border-bottom: 2px solid #2196F3;
    font-weight: 600;
}

QTabBar::tab:hover {
    background-color: #E3F2FD;
    color: #2196F3;
}

/* شريط الحالة */
QStatusBar {
    background-color: #FFFFFF;
    border-top: 1px solid #E0E0E0;
    color: #666666;
    padding: 4px;
}

QStatusBar::item {
    border: none;
}

/* شريط الأدوات */
QToolBar {
    background-color: #FFFFFF;
    border-bottom: 1px solid #E0E0E0;
    spacing: 4px;
    padding: 4px;
}

QToolBar::separator {
    background-color: #E0E0E0;
    width: 1px;
    margin: 4px 8px;
}

/* شريط القوائم */
QMenuBar {
    background-color: #FFFFFF;
    color: #212121;
    border-bottom: 1px solid #E0E0E0;
}

QMenuBar::item {
    padding: 6px 12px;
    background-color: transparent;
}

QMenuBar::item:selected {
    background-color: #E3F2FD;
    color: #2196F3;
}

QMenu {
    background-color: #FFFFFF;
    border: 1px solid #E0E0E0;
    border-radius: 6px;
    padding: 4px;
}

QMenu::item {
    padding: 6px 20px;
    border-radius: 4px;
}

QMenu::item:selected {
    background-color: #E3F2FD;
    color: #2196F3;
}

QMenu::separator {
    height: 1px;
    background-color: #E0E0E0;
    margin: 4px 8px;
}

/* التلميحات */
QToolTip {
    background-color: #424242;
    color: #FFFFFF;
    border: none;
    border-radius: 4px;
    padding: 6px 8px;
    font-size: 10px;
}

/* أشرطة التمرير */
QScrollBar:vertical {
    background-color: #F5F5F5;
    width: 12px;
    border-radius: 6px;
}

QScrollBar::handle:vertical {
    background-color: #BDBDBD;
    border-radius: 6px;
    min-height: 20px;
}

QScrollBar::handle:vertical:hover {
    background-color: #9E9E9E;
}

QScrollBar::add-line:vertical,
QScrollBar::sub-line:vertical {
    border: none;
    background: none;
}

QScrollBar:horizontal {
    background-color: #F5F5F5;
    height: 12px;
    border-radius: 6px;
}

QScrollBar::handle:horizontal {
    background-color: #BDBDBD;
    border-radius: 6px;
    min-width: 20px;
}

QScrollBar::handle:horizontal:hover {
    background-color: #9E9E9E;
}

QScrollBar::add-line:horizontal,
QScrollBar::sub-line:horizontal {
    border: none;
    background: none;
}

/* مربعات الاختيار */
QCheckBox {
    color: #212121;
    spacing: 8px;
}

QCheckBox::indicator {
    width: 16px;
    height: 16px;
    border: 2px solid #BDBDBD;
    border-radius: 3px;
    background-color: #FFFFFF;
}

QCheckBox::indicator:checked {
    background-color: #2196F3;
    border-color: #2196F3;
    image: url(:/icons/check.png);
}

QCheckBox::indicator:hover {
    border-color: #2196F3;
}

/* أزرار الراديو */
QRadioButton {
    color: #212121;
    spacing: 8px;
}

QRadioButton::indicator {
    width: 16px;
    height: 16px;
    border: 2px solid #BDBDBD;
    border-radius: 8px;
    background-color: #FFFFFF;
}

QRadioButton::indicator:checked {
    background-color: #2196F3;
    border-color: #2196F3;
}

QRadioButton::indicator:hover {
    border-color: #2196F3;
}

/* أشرطة التمرير المنزلقة */
QSlider::groove:horizontal {
    border: 1px solid #BDBDBD;
    height: 4px;
    background-color: #E0E0E0;
    border-radius: 2px;
}

QSlider::handle:horizontal {
    background-color: #2196F3;
    border: 1px solid #1976D2;
    width: 16px;
    margin: -6px 0;
    border-radius: 8px;
}

QSlider::handle:horizontal:hover {
    background-color: #1976D2;
}

/* مربعات التحرير والسرد */
QComboBox {
    background-color: #FFFFFF;
    border: 1px solid #E0E0E0;
    border-radius: 6px;
    padding: 6px 12px;
    color: #212121;
    min-width: 100px;
}

QComboBox:focus {
    border-color: #2196F3;
    border-width: 2px;
}

QComboBox::drop-down {
    border: none;
    width: 20px;
}

QComboBox::down-arrow {
    image: url(:/icons/arrow-down.png);
    width: 12px;
    height: 12px;
}

QComboBox QAbstractItemView {
    background-color: #FFFFFF;
    border: 1px solid #E0E0E0;
    border-radius: 6px;
    selection-background-color: #E3F2FD;
    selection-color: #2196F3;
}
