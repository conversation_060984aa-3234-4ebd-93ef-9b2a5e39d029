"""
ثيمات وألوان التطبيق
Application Themes and Colors
"""

from PyQt5.QtCore import QObject
from PyQt5.QtGui import QColor, QPalette

class ModernTheme:
    """الثيم العصري الرئيسي"""
    
    # الألوان الأساسية
    PRIMARY = "#2196F3"
    PRIMARY_DARK = "#1976D2"
    PRIMARY_LIGHT = "#BBDEFB"
    
    SECONDARY = "#FFC107"
    SECONDARY_DARK = "#F57C00"
    SECONDARY_LIGHT = "#FFF8E1"
    
    # ألوان الخلفية
    BACKGROUND = "#FAFAFA"
    SURFACE = "#FFFFFF"
    CARD = "#FFFFFF"
    
    # ألوان النص
    TEXT_PRIMARY = "#212121"
    TEXT_SECONDARY = "#757575"
    TEXT_DISABLED = "#BDBDBD"
    
    # ألوان الحالة
    SUCCESS = "#4CAF50"
    WARNING = "#FF9800"
    ERROR = "#F44336"
    INFO = "#2196F3"
    
    # ألوان الحدود
    BORDER = "#E0E0E0"
    DIVIDER = "#EEEEEE"
    
    # ألوان التفاعل
    HOVER = "#F5F5F5"
    PRESSED = "#EEEEEE"
    SELECTED = "#E3F2FD"
    
    # الظلال
    SHADOW_LIGHT = "rgba(0, 0, 0, 0.1)"
    SHADOW_MEDIUM = "rgba(0, 0, 0, 0.2)"
    SHADOW_DARK = "rgba(0, 0, 0, 0.3)"

class DarkTheme:
    """الثيم المظلم"""
    
    # الألوان الأساسية
    PRIMARY = "#2196F3"
    PRIMARY_DARK = "#1565C0"
    PRIMARY_LIGHT = "#42A5F5"
    
    SECONDARY = "#FFC107"
    SECONDARY_DARK = "#F57C00"
    SECONDARY_LIGHT = "#FFCA28"
    
    # ألوان الخلفية
    BACKGROUND = "#121212"
    SURFACE = "#1E1E1E"
    CARD = "#2D2D2D"
    
    # ألوان النص
    TEXT_PRIMARY = "#FFFFFF"
    TEXT_SECONDARY = "#B3B3B3"
    TEXT_DISABLED = "#666666"
    
    # ألوان الحالة
    SUCCESS = "#4CAF50"
    WARNING = "#FF9800"
    ERROR = "#F44336"
    INFO = "#2196F3"
    
    # ألوان الحدود
    BORDER = "#404040"
    DIVIDER = "#333333"
    
    # ألوان التفاعل
    HOVER = "#2A2A2A"
    PRESSED = "#333333"
    SELECTED = "#1E3A8A"
    
    # الظلال
    SHADOW_LIGHT = "rgba(0, 0, 0, 0.3)"
    SHADOW_MEDIUM = "rgba(0, 0, 0, 0.5)"
    SHADOW_DARK = "rgba(0, 0, 0, 0.7)"

class ThemeManager(QObject):
    """مدير الثيمات"""
    
    def __init__(self):
        super().__init__()
        self.current_theme = ModernTheme()
        self.is_dark_mode = False
    
    def get_stylesheet(self):
        """الحصول على ملف الأنماط الحالي"""
        theme = self.current_theme
        
        return f"""
        /* النافذة الرئيسية */
        QMainWindow {{
            background-color: {theme.BACKGROUND};
            color: {theme.TEXT_PRIMARY};
            font-family: 'Segoe UI', Arial, sans-serif;
        }}
        
        /* الشريط الجانبي */
        QFrame#sidebar {{
            background-color: {theme.SURFACE};
            border-right: 1px solid {theme.BORDER};
        }}
        
        /* الأزرار */
        QPushButton {{
            background-color: {theme.PRIMARY};
            color: white;
            border: none;
            border-radius: 8px;
            padding: 10px 20px;
            font-size: 11px;
            font-weight: bold;
        }}
        
        QPushButton:hover {{
            background-color: {theme.PRIMARY_DARK};
        }}
        
        QPushButton:pressed {{
            background-color: {theme.PRIMARY_DARK};
        }}
        
        QPushButton:disabled {{
            background-color: {theme.TEXT_DISABLED};
        }}
        
        /* أزرار ثانوية */
        QPushButton#secondary {{
            background-color: {theme.SURFACE};
            color: {theme.TEXT_PRIMARY};
            border: 1px solid {theme.BORDER};
        }}
        
        QPushButton#secondary:hover {{
            background-color: {theme.HOVER};
        }}
        
        /* حقول الإدخال */
        QLineEdit {{
            background-color: {theme.SURFACE};
            border: 1px solid {theme.BORDER};
            border-radius: 6px;
            padding: 8px 12px;
            font-size: 11px;
            color: {theme.TEXT_PRIMARY};
        }}
        
        QLineEdit:focus {{
            border-color: {theme.PRIMARY};
        }}
        
        /* القوائم */
        QListWidget {{
            background-color: {theme.SURFACE};
            border: 1px solid {theme.BORDER};
            border-radius: 6px;
            alternate-background-color: {theme.HOVER};
        }}
        
        QListWidget::item {{
            padding: 8px;
            border-bottom: 1px solid {theme.DIVIDER};
        }}
        
        QListWidget::item:selected {{
            background-color: {theme.SELECTED};
            color: {theme.PRIMARY};
        }}
        
        /* شريط التقدم */
        QProgressBar {{
            border: 1px solid {theme.BORDER};
            border-radius: 6px;
            text-align: center;
            background-color: {theme.SURFACE};
        }}
        
        QProgressBar::chunk {{
            background-color: {theme.PRIMARY};
            border-radius: 5px;
        }}
        
        /* التبويبات */
        QTabWidget::pane {{
            border: 1px solid {theme.BORDER};
            background-color: {theme.SURFACE};
        }}
        
        QTabBar::tab {{
            background-color: {theme.BACKGROUND};
            padding: 8px 16px;
            margin-right: 2px;
            border-top-left-radius: 6px;
            border-top-right-radius: 6px;
        }}
        
        QTabBar::tab:selected {{
            background-color: {theme.SURFACE};
            border-bottom: 2px solid {theme.PRIMARY};
        }}
        
        /* شريط الحالة */
        QStatusBar {{
            background-color: {theme.SURFACE};
            border-top: 1px solid {theme.BORDER};
            color: {theme.TEXT_SECONDARY};
        }}
        
        /* التلميحات */
        QToolTip {{
            background-color: {theme.CARD};
            color: {theme.TEXT_PRIMARY};
            border: 1px solid {theme.BORDER};
            border-radius: 4px;
            padding: 4px 8px;
        }}
        """
    
    def toggle_theme(self):
        """تبديل بين الثيم الفاتح والمظلم"""
        if self.is_dark_mode:
            self.current_theme = ModernTheme()
            self.is_dark_mode = False
        else:
            self.current_theme = DarkTheme()
            self.is_dark_mode = True
    
    def apply_theme(self, widget):
        """تطبيق الثيم على عنصر واجهة"""
        widget.setStyleSheet(self.get_stylesheet())

# إنشاء مثيل عام من مدير الثيمات
theme_manager = ThemeManager()
