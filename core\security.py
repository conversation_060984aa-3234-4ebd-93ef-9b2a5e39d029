"""
وحدة الأمان والحماية - التوقيع الإلكتروني وحماية PDF
Security Module - Digital Signatures and PDF Protection
"""

import os
import hashlib
import secrets
from pathlib import Path
from typing import Optional, Dict, Any, List
from PyQt5.QtCore import QObject, pyqtSignal, QThread
import pikepdf
import fitz  # PyMuPDF
from PIL import Image, ImageDraw, ImageFont
import tempfile
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.asymmetric import rsa, padding
from cryptography.hazmat.primitives.serialization import Encoding, PrivateFormat, NoEncryption
import base64

from config.settings import ProcessingSettings

class SecurityOperation(QThread):
    """خيط عمليات الأمان"""
    
    progress_updated = pyqtSignal(int)
    operation_completed = pyqtSignal(bool, str)
    status_updated = pyqtSignal(str)
    
    def __init__(self, operation_type, **kwargs):
        super().__init__()
        self.operation_type = operation_type
        self.kwargs = kwargs
        self.is_cancelled = False
    
    def run(self):
        """تشغيل عملية الأمان"""
        try:
            if self.operation_type == "encrypt":
                self.encrypt_pdf()
            elif self.operation_type == "decrypt":
                self.decrypt_pdf()
            elif self.operation_type == "add_signature":
                self.add_signature()
            elif self.operation_type == "remove_password":
                self.remove_password()
            else:
                self.operation_completed.emit(False, "عملية أمان غير مدعومة")
        except Exception as e:
            self.operation_completed.emit(False, f"خطأ في عملية الأمان: {str(e)}")
    
    def encrypt_pdf(self):
        """تشفير ملف PDF بكلمة مرور"""
        input_file = self.kwargs.get('input_file', '')
        output_file = self.kwargs.get('output_file', '')
        user_password = self.kwargs.get('user_password', '')
        owner_password = self.kwargs.get('owner_password', user_password)
        
        if not all([input_file, output_file, user_password]):
            self.operation_completed.emit(False, "معلومات التشفير غير مكتملة")
            return
        
        self.status_updated.emit("بدء تشفير الملف...")
        
        try:
            with pikepdf.open(input_file) as pdf:
                self.progress_updated.emit(50)
                
                # تطبيق التشفير
                pdf.save(
                    output_file,
                    encryption=pikepdf.Encryption(
                        user=user_password,
                        owner=owner_password,
                        R=4,  # مستوى التشفير
                        allow=pikepdf.Permissions(
                            accessibility=True,
                            extract=False,
                            modify_annotation=False,
                            modify_assembly=False,
                            modify_form=False,
                            modify_other=False,
                            print_lowres=True,
                            print_highres=True
                        )
                    )
                )
                
                self.progress_updated.emit(100)
                self.operation_completed.emit(True, "تم تشفير الملف بنجاح")
                
        except Exception as e:
            self.operation_completed.emit(False, f"خطأ في تشفير الملف: {str(e)}")
    
    def decrypt_pdf(self):
        """فك تشفير ملف PDF"""
        input_file = self.kwargs.get('input_file', '')
        output_file = self.kwargs.get('output_file', '')
        password = self.kwargs.get('password', '')
        
        if not all([input_file, output_file, password]):
            self.operation_completed.emit(False, "معلومات فك التشفير غير مكتملة")
            return
        
        self.status_updated.emit("بدء فك تشفير الملف...")
        
        try:
            with pikepdf.open(input_file, password=password) as pdf:
                self.progress_updated.emit(50)
                
                # حفظ الملف بدون تشفير
                pdf.save(output_file)
                
                self.progress_updated.emit(100)
                self.operation_completed.emit(True, "تم فك تشفير الملف بنجاح")
                
        except pikepdf.PasswordError:
            self.operation_completed.emit(False, "كلمة المرور غير صحيحة")
        except Exception as e:
            self.operation_completed.emit(False, f"خطأ في فك التشفير: {str(e)}")
    
    def add_signature(self):
        """إضافة توقيع إلكتروني"""
        input_file = self.kwargs.get('input_file', '')
        output_file = self.kwargs.get('output_file', '')
        signature_type = self.kwargs.get('signature_type', 'text')  # text, image, draw
        signature_data = self.kwargs.get('signature_data', '')
        position = self.kwargs.get('position', (100, 100))
        page_number = self.kwargs.get('page_number', 0)
        
        if not all([input_file, output_file, signature_data]):
            self.operation_completed.emit(False, "معلومات التوقيع غير مكتملة")
            return
        
        self.status_updated.emit("إضافة التوقيع الإلكتروني...")
        
        try:
            doc = fitz.open(input_file)
            
            if page_number >= len(doc):
                page_number = len(doc) - 1
            
            page = doc[page_number]
            
            if signature_type == 'text':
                # توقيع نصي
                self.add_text_signature(page, signature_data, position)
            elif signature_type == 'image':
                # توقيع من صورة
                self.add_image_signature(page, signature_data, position)
            elif signature_type == 'draw':
                # توقيع مرسوم
                self.add_drawn_signature(page, signature_data, position)
            
            self.progress_updated.emit(80)
            
            # حفظ الملف
            doc.save(output_file)
            doc.close()
            
            self.progress_updated.emit(100)
            self.operation_completed.emit(True, "تم إضافة التوقيع بنجاح")
            
        except Exception as e:
            self.operation_completed.emit(False, f"خطأ في إضافة التوقيع: {str(e)}")
    
    def add_text_signature(self, page, text, position):
        """إضافة توقيع نصي"""
        rect = fitz.Rect(position[0], position[1], position[0] + 200, position[1] + 50)
        page.insert_textbox(rect, text, fontsize=12, color=(0, 0, 1))
    
    def add_image_signature(self, page, image_path, position):
        """إضافة توقيع من صورة"""
        if os.path.exists(image_path):
            rect = fitz.Rect(position[0], position[1], position[0] + 150, position[1] + 75)
            page.insert_image(rect, filename=image_path)
    
    def add_drawn_signature(self, page, signature_points, position):
        """إضافة توقيع مرسوم"""
        # إنشاء صورة مؤقتة للتوقيع المرسوم
        temp_image = self.create_signature_image(signature_points)
        if temp_image:
            rect = fitz.Rect(position[0], position[1], position[0] + 150, position[1] + 75)
            page.insert_image(rect, filename=temp_image)
            os.unlink(temp_image)  # حذف الملف المؤقت
    
    def create_signature_image(self, points):
        """إنشاء صورة التوقيع من النقاط"""
        try:
            # إنشاء صورة فارغة
            img = Image.new('RGBA', (300, 150), (255, 255, 255, 0))
            draw = ImageDraw.Draw(img)
            
            # رسم التوقيع
            if len(points) > 1:
                draw.line(points, fill=(0, 0, 255, 255), width=3)
            
            # حفظ الصورة مؤقتاً
            temp_file = tempfile.NamedTemporaryFile(suffix='.png', delete=False)
            img.save(temp_file.name, 'PNG')
            temp_file.close()
            
            return temp_file.name
        except:
            return None
    
    def remove_password(self):
        """إزالة كلمة المرور من PDF"""
        input_file = self.kwargs.get('input_file', '')
        output_file = self.kwargs.get('output_file', '')
        password = self.kwargs.get('password', '')
        
        if not all([input_file, output_file, password]):
            self.operation_completed.emit(False, "معلومات إزالة كلمة المرور غير مكتملة")
            return
        
        self.status_updated.emit("إزالة كلمة المرور...")
        
        try:
            with pikepdf.open(input_file, password=password) as pdf:
                self.progress_updated.emit(50)
                
                # حفظ بدون حماية
                pdf.save(output_file)
                
                self.progress_updated.emit(100)
                self.operation_completed.emit(True, "تم إزالة كلمة المرور بنجاح")
                
        except pikepdf.PasswordError:
            self.operation_completed.emit(False, "كلمة المرور غير صحيحة")
        except Exception as e:
            self.operation_completed.emit(False, f"خطأ في إزالة كلمة المرور: {str(e)}")
    
    def cancel(self):
        """إلغاء العملية"""
        self.is_cancelled = True

class SecurityManager(QObject):
    """مدير الأمان والحماية"""
    
    def __init__(self):
        super().__init__()
        self.current_operation = None
    
    def start_security_operation(self, operation_type: str, **kwargs) -> SecurityOperation:
        """بدء عملية أمان جديدة"""
        if self.current_operation and self.current_operation.isRunning():
            self.current_operation.cancel()
            self.current_operation.wait()
        
        self.current_operation = SecurityOperation(operation_type, **kwargs)
        return self.current_operation
    
    def cancel_current_operation(self):
        """إلغاء العملية الحالية"""
        if self.current_operation and self.current_operation.isRunning():
            self.current_operation.cancel()
    
    def generate_password(self, length: int = 12) -> str:
        """توليد كلمة مرور قوية"""
        import string
        characters = string.ascii_letters + string.digits + "!@#$%^&*"
        return ''.join(secrets.choice(characters) for _ in range(length))
    
    def check_password_strength(self, password: str) -> Dict[str, Any]:
        """فحص قوة كلمة المرور"""
        score = 0
        feedback = []
        
        if len(password) >= 8:
            score += 1
        else:
            feedback.append("يجب أن تكون كلمة المرور 8 أحرف على الأقل")
        
        if any(c.isupper() for c in password):
            score += 1
        else:
            feedback.append("يجب أن تحتوي على حرف كبير")
        
        if any(c.islower() for c in password):
            score += 1
        else:
            feedback.append("يجب أن تحتوي على حرف صغير")
        
        if any(c.isdigit() for c in password):
            score += 1
        else:
            feedback.append("يجب أن تحتوي على رقم")
        
        if any(c in "!@#$%^&*()_+-=[]{}|;:,.<>?" for c in password):
            score += 1
        else:
            feedback.append("يجب أن تحتوي على رمز خاص")
        
        strength_levels = {
            0: "ضعيف جداً",
            1: "ضعيف",
            2: "متوسط",
            3: "جيد",
            4: "قوي",
            5: "قوي جداً"
        }
        
        return {
            'score': score,
            'strength': strength_levels.get(score, "غير معروف"),
            'feedback': feedback
        }
    
    def is_pdf_encrypted(self, file_path: str) -> bool:
        """التحقق من تشفير ملف PDF"""
        try:
            doc = fitz.open(file_path)
            encrypted = doc.needs_pass
            doc.close()
            return encrypted
        except:
            return False
    
    def get_pdf_permissions(self, file_path: str, password: str = "") -> Dict[str, bool]:
        """الحصول على صلاحيات ملف PDF"""
        try:
            if password:
                pdf = pikepdf.open(file_path, password=password)
            else:
                pdf = pikepdf.open(file_path)
            
            # استخراج معلومات الصلاحيات
            permissions = {
                'print': True,
                'modify': True,
                'copy': True,
                'add_notes': True
            }
            
            # فحص الصلاحيات الفعلية إذا كان الملف محمي
            if hasattr(pdf, 'encryption'):
                enc = pdf.encryption
                if enc:
                    permissions.update({
                        'print': enc.allow.print_lowres or enc.allow.print_highres,
                        'modify': enc.allow.modify_other,
                        'copy': enc.allow.extract,
                        'add_notes': enc.allow.modify_annotation
                    })
            
            pdf.close()
            return permissions
            
        except Exception as e:
            return {'error': str(e)}
    
    def create_digital_certificate(self) -> Dict[str, str]:
        """إنشاء شهادة رقمية للتوقيع"""
        try:
            # إنشاء مفتاح خاص
            private_key = rsa.generate_private_key(
                public_exponent=65537,
                key_size=2048
            )
            
            # تحويل المفتاح إلى تنسيق PEM
            private_pem = private_key.private_bytes(
                encoding=Encoding.PEM,
                format=PrivateFormat.PKCS8,
                encryption_algorithm=NoEncryption()
            )
            
            # الحصول على المفتاح العام
            public_key = private_key.public_key()
            
            return {
                'private_key': base64.b64encode(private_pem).decode(),
                'public_key': base64.b64encode(
                    public_key.public_bytes(
                        encoding=Encoding.PEM,
                        format=serialization.PublicFormat.SubjectPublicKeyInfo
                    )
                ).decode(),
                'created': True
            }
            
        except Exception as e:
            return {'error': str(e), 'created': False}
