#!/usr/bin/env python3
"""
اختبار سريع لتطبيق PDF Editor Pro
Quick test for PDF Editor Pro
"""

import sys
import os
from pathlib import Path

# إضافة مسار المشروع
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def main():
    """اختبار سريع للتطبيق"""
    print("🚀 PDF Editor Pro - اختبار سريع")
    print("🚀 PDF Editor Pro - Quick Test")
    print("=" * 40)
    
    try:
        # اختبار الاستيراد الأساسي
        print("📦 اختبار الاستيراد...")
        from PyQt5.QtWidgets import QApplication
        from config.settings import AppSettings
        from ui.main_window import MainWindow
        
        print("✅ تم الاستيراد بنجاح!")
        
        # إنشاء التطبيق
        print("🎯 إنشاء التطبيق...")
        app = QApplication(sys.argv)
        
        # إنشاء النافذة الرئيسية
        print("🖥️ إنشاء النافذة الرئيسية...")
        window = MainWindow()
        
        print("✅ تم إنشاء التطبيق بنجاح!")
        print("🎉 التطبيق جاهز للتشغيل!")
        
        # عرض النافذة
        window.show()
        
        print("\n💡 لإغلاق التطبيق، أغلق النافذة أو اضغط Ctrl+C")
        print("💡 To close the app, close the window or press Ctrl+C")
        
        # تشغيل التطبيق
        sys.exit(app.exec_())
        
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف التطبيق")
        print("⏹️ Application stopped")
        
    except ImportError as e:
        print(f"\n❌ خطأ في الاستيراد: {e}")
        print("❌ Import error")
        print("\n💡 تأكد من تثبيت المكتبات المطلوبة:")
        print("💡 Make sure to install required packages:")
        print("pip install -r requirements.txt")
        
    except Exception as e:
        print(f"\n❌ خطأ: {e}")
        print(f"❌ Error: {e}")
        
        import traceback
        print("\n🔧 تفاصيل الخطأ:")
        print("🔧 Error details:")
        traceback.print_exc()

if __name__ == "__main__":
    main()
