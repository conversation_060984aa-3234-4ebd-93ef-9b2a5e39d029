@echo off
chcp 65001 >nul
title PDF Editor Pro - تطبيق تحرير ملفات PDF

echo.
echo ========================================
echo   PDF Editor Pro - تطبيق تحرير ملفات PDF
echo   PDF Editor Pro - PDF Editing App
echo ========================================
echo.

REM التحقق من وجود Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت أو غير موجود في PATH
    echo ❌ Python is not installed or not in PATH
    echo.
    echo يرجى تثبيت Python 3.7 أو أحدث من:
    echo Please install Python 3.7 or newer from:
    echo https://www.python.org/downloads/
    echo.
    pause
    exit /b 1
)

REM عرض إصدار Python
echo 🐍 إصدار Python المثبت:
echo 🐍 Installed Python version:
python --version
echo.

REM التحقق من وجود pip
pip --version >nul 2>&1
if errorlevel 1 (
    echo ❌ pip غير متوفر
    echo ❌ pip is not available
    pause
    exit /b 1
)

REM التحقق من وجود ملف requirements.txt
if not exist "requirements.txt" (
    echo ❌ ملف requirements.txt غير موجود
    echo ❌ requirements.txt file not found
    pause
    exit /b 1
)

REM سؤال المستخدم عن تثبيت المكتبات
echo 📦 هل تريد تثبيت/تحديث المكتبات المطلوبة؟
echo 📦 Do you want to install/update required packages?
echo.
echo [Y] نعم / Yes
echo [N] لا / No
echo [S] تخطي والتشغيل مباشرة / Skip and run directly
echo.
set /p choice="اختر (Y/N/S): "

if /i "%choice%"=="Y" (
    echo.
    echo 📦 تثبيت المكتبات المطلوبة...
    echo 📦 Installing required packages...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo.
        echo ❌ فشل في تثبيت بعض المكتبات
        echo ❌ Failed to install some packages
        echo.
        echo جرب تشغيل الأمر التالي يدوياً:
        echo Try running this command manually:
        echo pip install -r requirements.txt
        echo.
        pause
        exit /b 1
    )
    echo.
    echo ✅ تم تثبيت المكتبات بنجاح
    echo ✅ Packages installed successfully
    echo.
)

if /i "%choice%"=="N" (
    echo.
    echo ⏭️ تم تخطي تثبيت المكتبات
    echo ⏭️ Package installation skipped
    echo.
    pause
    exit /b 0
)

REM تشغيل التطبيق
echo 🚀 تشغيل PDF Editor Pro...
echo 🚀 Running PDF Editor Pro...
echo.

python run.py

if errorlevel 1 (
    echo.
    echo ❌ حدث خطأ أثناء تشغيل التطبيق
    echo ❌ An error occurred while running the application
    echo.
    echo للحصول على مساعدة، تأكد من:
    echo For help, make sure:
    echo 1. تثبيت جميع المكتبات المطلوبة
    echo    All required packages are installed
    echo 2. استخدام Python 3.7 أو أحدث
    echo    Using Python 3.7 or newer
    echo 3. وجود جميع ملفات المشروع
    echo    All project files are present
    echo.
)

echo.
echo 👋 شكراً لاستخدام PDF Editor Pro
echo 👋 Thank you for using PDF Editor Pro
echo.
pause
