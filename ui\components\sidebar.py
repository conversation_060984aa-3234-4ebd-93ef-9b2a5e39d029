"""
الشريط الجانبي للتطبيق
Application Sidebar Component
"""

from PyQt5.QtWidgets import (QFrame, QVBoxLayout, QHBoxLayout, QPushButton, 
                             QLabel, QScrollArea, QWidget, QSpacerItem, 
                             QSizePolicy)
from PyQt5.QtCore import Qt, pyqtSignal, QPropertyAnimation, QEasingCurve
from PyQt5.QtGui import QFont, QIcon
import qtawesome as qta

from config.settings import UISettings

class SidebarButton(QPushButton):
    """زر الشريط الجانبي المخصص"""
    
    def __init__(self, text, icon_name, parent=None):
        super().__init__(parent)
        self.setText(text)
        self.setIcon(qta.icon(icon_name, color='white'))
        self.setIconSize(UISettings.ICON_SIZE_NORMAL, UISettings.ICON_SIZE_NORMAL)
        self.setFixedHeight(50)
        self.setObjectName("sidebarButton")
        
        # تطبيق الأنماط
        self.setStyleSheet("""
            QPushButton#sidebarButton {
                text-align: left;
                padding: 10px 15px;
                border: none;
                border-radius: 8px;
                background-color: transparent;
                color: #666;
                font-size: 12px;
                font-weight: 500;
            }
            
            QPushButton#sidebarButton:hover {
                background-color: #E3F2FD;
                color: #2196F3;
            }
            
            QPushButton#sidebarButton:pressed {
                background-color: #BBDEFB;
            }
            
            QPushButton#sidebarButton:checked {
                background-color: #2196F3;
                color: white;
            }
        """)
        
        self.setCheckable(True)

class Sidebar(QFrame):
    """الشريط الجانبي الرئيسي"""
    
    # إشارات
    feature_selected = pyqtSignal(str)  # إشارة اختيار ميزة
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setObjectName("sidebar")
        self.setFixedWidth(UISettings.SIDEBAR_WIDTH)
        self.current_button = None
        
        self.setup_ui()
        self.setup_animations()
    
    def setup_ui(self):
        """إعداد واجهة الشريط الجانبي"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 20, 10, 20)
        layout.setSpacing(UISettings.SPACING_NORMAL)
        
        # شعار التطبيق
        self.create_header(layout)
        
        # منطقة التمرير للأزرار
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setFrameShape(QFrame.NoFrame)
        
        # محتوى الأزرار
        buttons_widget = QWidget()
        buttons_layout = QVBoxLayout(buttons_widget)
        buttons_layout.setContentsMargins(0, 0, 0, 0)
        buttons_layout.setSpacing(5)
        
        # إنشاء أزرار الميزات
        self.create_feature_buttons(buttons_layout)
        
        # مساحة فارغة في النهاية
        buttons_layout.addItem(QSpacerItem(20, 40, QSizePolicy.Minimum, QSizePolicy.Expanding))
        
        scroll_area.setWidget(buttons_widget)
        layout.addWidget(scroll_area)
        
        # إعدادات في الأسفل
        self.create_footer(layout)
    
    def create_header(self, layout):
        """إنشاء رأس الشريط الجانبي"""
        header_frame = QFrame()
        header_layout = QVBoxLayout(header_frame)
        header_layout.setContentsMargins(0, 0, 0, 20)
        
        # أيقونة التطبيق
        app_icon = QLabel()
        app_icon.setPixmap(qta.icon('fa5s.file-pdf', color='#2196F3').pixmap(48, 48))
        app_icon.setAlignment(Qt.AlignCenter)
        
        # اسم التطبيق
        app_name = QLabel("PDF Editor Pro")
        app_name.setAlignment(Qt.AlignCenter)
        app_name.setFont(QFont("Segoe UI", 14, QFont.Bold))
        app_name.setStyleSheet("color: #2196F3; margin-bottom: 10px;")
        
        header_layout.addWidget(app_icon)
        header_layout.addWidget(app_name)
        
        layout.addWidget(header_frame)
    
    def create_feature_buttons(self, layout):
        """إنشاء أزرار الميزات"""
        features = [
            ("إنشاء PDF", "fa5s.plus-circle", "create"),
            ("دمج ملفات", "fa5s.layer-group", "merge"),
            ("تقسيم ملف", "fa5s.cut", "split"),
            ("ضغط ملف", "fa5s.compress-arrows-alt", "compress"),
            ("تحويل إلى Word", "fa5b.microsoft", "convert_word"),
            ("تحويل إلى Excel", "fa5s.table", "convert_excel"),
            ("تحويل إلى PowerPoint", "fa5s.presentation", "convert_ppt"),
            ("تحويل الصور", "fa5s.images", "convert_images"),
            ("تحرير المحتوى", "fa5s.edit", "edit"),
            ("التوقيع الإلكتروني", "fa5s.signature", "signature"),
            ("حماية بكلمة مرور", "fa5s.lock", "password"),
            ("إصلاح ملف معطوب", "fa5s.tools", "repair"),
            ("مقارنة ملفات", "fa5s.not-equal", "compare")
        ]
        
        self.buttons = {}
        
        for text, icon, feature_id in features:
            button = SidebarButton(text, icon)
            button.clicked.connect(lambda checked, fid=feature_id: self.on_feature_selected(fid))
            self.buttons[feature_id] = button
            layout.addWidget(button)
    
    def create_footer(self, layout):
        """إنشاء تذييل الشريط الجانبي"""
        footer_frame = QFrame()
        footer_layout = QVBoxLayout(footer_frame)
        footer_layout.setContentsMargins(0, 20, 0, 0)
        
        # زر الإعدادات
        settings_btn = SidebarButton("الإعدادات", "fa5s.cog")
        settings_btn.clicked.connect(lambda: self.feature_selected.emit("settings"))
        
        # زر المساعدة
        help_btn = SidebarButton("المساعدة", "fa5s.question-circle")
        help_btn.clicked.connect(lambda: self.feature_selected.emit("help"))
        
        footer_layout.addWidget(settings_btn)
        footer_layout.addWidget(help_btn)
        
        layout.addWidget(footer_frame)
    
    def setup_animations(self):
        """إعداد الحركات والتأثيرات"""
        self.slide_animation = QPropertyAnimation(self, b"geometry")
        self.slide_animation.setDuration(UISettings.ANIMATION_DURATION)
        self.slide_animation.setEasingCurve(QEasingCurve.OutCubic)
    
    def on_feature_selected(self, feature_id):
        """معالج اختيار الميزة"""
        # إلغاء تحديد الزر السابق
        if self.current_button:
            self.current_button.setChecked(False)
        
        # تحديد الزر الحالي
        current_button = self.buttons.get(feature_id)
        if current_button:
            current_button.setChecked(True)
            self.current_button = current_button
        
        # إرسال إشارة اختيار الميزة
        self.feature_selected.emit(feature_id)
    
    def select_feature(self, feature_id):
        """تحديد ميزة برمجياً"""
        if feature_id in self.buttons:
            self.on_feature_selected(feature_id)
    
    def toggle_sidebar(self):
        """إظهار/إخفاء الشريط الجانبي"""
        if self.isVisible():
            self.hide_sidebar()
        else:
            self.show_sidebar()
    
    def hide_sidebar(self):
        """إخفاء الشريط الجانبي"""
        self.slide_animation.setStartValue(self.geometry())
        self.slide_animation.setEndValue(self.geometry().translated(-self.width(), 0))
        self.slide_animation.finished.connect(self.hide)
        self.slide_animation.start()
    
    def show_sidebar(self):
        """إظهار الشريط الجانبي"""
        self.show()
        self.slide_animation.setStartValue(self.geometry().translated(-self.width(), 0))
        self.slide_animation.setEndValue(self.geometry())
        self.slide_animation.start()
