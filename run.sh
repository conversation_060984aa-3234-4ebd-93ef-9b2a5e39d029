#!/bin/bash

# PDF Editor Pro - تطبيق تحرير ملفات PDF
# PDF Editor Pro - PDF Editing Application

# ألوان للنص
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# دالة طباعة العنوان
print_header() {
    echo -e "${BLUE}========================================${NC}"
    echo -e "${PURPLE}  PDF Editor Pro - تطبيق تحرير ملفات PDF${NC}"
    echo -e "${PURPLE}  PDF Editor Pro - PDF Editing App${NC}"
    echo -e "${BLUE}========================================${NC}"
    echo
}

# دالة التحقق من Python
check_python() {
    echo -e "${CYAN}🐍 فحص Python...${NC}"
    echo -e "${CYAN}🐍 Checking Python...${NC}"
    
    if ! command -v python3 &> /dev/null; then
        if ! command -v python &> /dev/null; then
            echo -e "${RED}❌ Python غير مثبت${NC}"
            echo -e "${RED}❌ Python is not installed${NC}"
            echo
            echo -e "${YELLOW}يرجى تثبيت Python 3.7 أو أحدث${NC}"
            echo -e "${YELLOW}Please install Python 3.7 or newer${NC}"
            echo -e "${YELLOW}Ubuntu/Debian: sudo apt install python3 python3-pip${NC}"
            echo -e "${YELLOW}CentOS/RHEL: sudo yum install python3 python3-pip${NC}"
            echo -e "${YELLOW}macOS: brew install python3${NC}"
            return 1
        else
            PYTHON_CMD="python"
        fi
    else
        PYTHON_CMD="python3"
    fi
    
    # التحقق من إصدار Python
    PYTHON_VERSION=$($PYTHON_CMD --version 2>&1 | cut -d' ' -f2)
    echo -e "${GREEN}✅ Python $PYTHON_VERSION${NC}"
    
    # التحقق من أن الإصدار 3.7 أو أحدث
    PYTHON_MAJOR=$($PYTHON_CMD -c "import sys; print(sys.version_info.major)")
    PYTHON_MINOR=$($PYTHON_CMD -c "import sys; print(sys.version_info.minor)")
    
    if [ "$PYTHON_MAJOR" -lt 3 ] || ([ "$PYTHON_MAJOR" -eq 3 ] && [ "$PYTHON_MINOR" -lt 7 ]); then
        echo -e "${RED}❌ يتطلب Python 3.7 أو أحدث${NC}"
        echo -e "${RED}❌ Python 3.7 or newer is required${NC}"
        return 1
    fi
    
    return 0
}

# دالة التحقق من pip
check_pip() {
    echo -e "${CYAN}📦 فحص pip...${NC}"
    echo -e "${CYAN}📦 Checking pip...${NC}"
    
    if ! command -v pip3 &> /dev/null; then
        if ! command -v pip &> /dev/null; then
            echo -e "${RED}❌ pip غير متوفر${NC}"
            echo -e "${RED}❌ pip is not available${NC}"
            return 1
        else
            PIP_CMD="pip"
        fi
    else
        PIP_CMD="pip3"
    fi
    
    echo -e "${GREEN}✅ pip متوفر${NC}"
    echo -e "${GREEN}✅ pip is available${NC}"
    return 0
}

# دالة تثبيت المكتبات
install_packages() {
    echo -e "${CYAN}📦 تثبيت المكتبات المطلوبة...${NC}"
    echo -e "${CYAN}📦 Installing required packages...${NC}"
    echo
    
    if [ ! -f "requirements.txt" ]; then
        echo -e "${RED}❌ ملف requirements.txt غير موجود${NC}"
        echo -e "${RED}❌ requirements.txt file not found${NC}"
        return 1
    fi
    
    $PIP_CMD install -r requirements.txt
    
    if [ $? -eq 0 ]; then
        echo
        echo -e "${GREEN}✅ تم تثبيت المكتبات بنجاح${NC}"
        echo -e "${GREEN}✅ Packages installed successfully${NC}"
        return 0
    else
        echo
        echo -e "${RED}❌ فشل في تثبيت بعض المكتبات${NC}"
        echo -e "${RED}❌ Failed to install some packages${NC}"
        echo
        echo -e "${YELLOW}جرب تشغيل الأمر التالي يدوياً:${NC}"
        echo -e "${YELLOW}Try running this command manually:${NC}"
        echo -e "${YELLOW}$PIP_CMD install -r requirements.txt${NC}"
        return 1
    fi
}

# دالة تشغيل التطبيق
run_app() {
    echo -e "${CYAN}🚀 تشغيل PDF Editor Pro...${NC}"
    echo -e "${CYAN}🚀 Running PDF Editor Pro...${NC}"
    echo
    
    $PYTHON_CMD run.py
    
    if [ $? -ne 0 ]; then
        echo
        echo -e "${RED}❌ حدث خطأ أثناء تشغيل التطبيق${NC}"
        echo -e "${RED}❌ An error occurred while running the application${NC}"
        echo
        echo -e "${YELLOW}للحصول على مساعدة، تأكد من:${NC}"
        echo -e "${YELLOW}For help, make sure:${NC}"
        echo -e "${YELLOW}1. تثبيت جميع المكتبات المطلوبة${NC}"
        echo -e "${YELLOW}   All required packages are installed${NC}"
        echo -e "${YELLOW}2. استخدام Python 3.7 أو أحدث${NC}"
        echo -e "${YELLOW}   Using Python 3.7 or newer${NC}"
        echo -e "${YELLOW}3. وجود جميع ملفات المشروع${NC}"
        echo -e "${YELLOW}   All project files are present${NC}"
        return 1
    fi
    
    return 0
}

# الدالة الرئيسية
main() {
    print_header
    
    # التحقق من Python
    if ! check_python; then
        echo
        read -p "اضغط Enter للخروج / Press Enter to exit..."
        exit 1
    fi
    
    echo
    
    # التحقق من pip
    if ! check_pip; then
        echo
        read -p "اضغط Enter للخروج / Press Enter to exit..."
        exit 1
    fi
    
    echo
    
    # سؤال المستخدم عن تثبيت المكتبات
    echo -e "${YELLOW}📦 هل تريد تثبيت/تحديث المكتبات المطلوبة؟${NC}"
    echo -e "${YELLOW}📦 Do you want to install/update required packages?${NC}"
    echo
    echo -e "${GREEN}[y] نعم / Yes${NC}"
    echo -e "${RED}[n] لا / No${NC}"
    echo -e "${BLUE}[s] تخطي والتشغيل مباشرة / Skip and run directly${NC}"
    echo
    read -p "اختر (y/n/s): " choice
    
    case $choice in
        [Yy]* )
            echo
            if ! install_packages; then
                echo
                read -p "اضغط Enter للخروج / Press Enter to exit..."
                exit 1
            fi
            ;;
        [Nn]* )
            echo
            echo -e "${YELLOW}⏭️ تم تخطي تثبيت المكتبات${NC}"
            echo -e "${YELLOW}⏭️ Package installation skipped${NC}"
            echo
            read -p "اضغط Enter للخروج / Press Enter to exit..."
            exit 0
            ;;
        [Ss]* )
            echo
            echo -e "${BLUE}⏭️ تخطي تثبيت المكتبات والتشغيل مباشرة${NC}"
            echo -e "${BLUE}⏭️ Skipping package installation and running directly${NC}"
            ;;
        * )
            echo
            echo -e "${YELLOW}⏭️ اختيار غير صحيح، تخطي تثبيت المكتبات${NC}"
            echo -e "${YELLOW}⏭️ Invalid choice, skipping package installation${NC}"
            ;;
    esac
    
    echo
    
    # تشغيل التطبيق
    run_app
    
    echo
    echo -e "${PURPLE}👋 شكراً لاستخدام PDF Editor Pro${NC}"
    echo -e "${PURPLE}👋 Thank you for using PDF Editor Pro${NC}"
    echo
}

# تشغيل الدالة الرئيسية
main "$@"
