#!/usr/bin/env python3
"""
إعداد التثبيت لتطبيق PDF Editor Pro
Setup configuration for PDF Editor Pro
"""

from setuptools import setup, find_packages
from pathlib import Path

# قراءة ملف README
this_directory = Path(__file__).parent
long_description = (this_directory / "README.md").read_text(encoding='utf-8')

# قراءة المتطلبات
requirements = []
with open('requirements.txt', 'r', encoding='utf-8') as f:
    requirements = [line.strip() for line in f if line.strip() and not line.startswith('#')]

setup(
    name="pdf-editor-pro",
    version="1.0.0",
    author="PDF Editor Team",
    author_email="<EMAIL>",
    description="تطبيق ذكي وعصري لتحرير ملفات PDF - Smart and Modern PDF Editor",
    long_description=long_description,
    long_description_content_type="text/markdown",
    url="https://github.com/your-username/pdf-editor-pro",
    packages=find_packages(),
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: End Users/Desktop",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.7",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Topic :: Office/Business",
        "Topic :: Multimedia :: Graphics :: Graphics Conversion",
        "Topic :: Text Processing",
        "Environment :: X11 Applications :: Qt",
    ],
    python_requires=">=3.7",
    install_requires=requirements,
    extras_require={
        "dev": [
            "pytest>=6.0",
            "black>=21.0",
            "flake8>=3.8",
            "mypy>=0.800",
        ],
        "build": [
            "pyinstaller>=4.0",
            "cx_Freeze>=6.0",
        ]
    },
    entry_points={
        "console_scripts": [
            "pdf-editor-pro=main:main",
        ],
        "gui_scripts": [
            "pdf-editor-pro-gui=main:main",
        ]
    },
    include_package_data=True,
    package_data={
        "": ["*.qss", "*.css", "*.png", "*.jpg", "*.svg", "*.ico", "*.qm", "*.ts"],
        "resources": ["*"],
        "resources/icons": ["*"],
        "resources/styles": ["*"],
        "resources/translations": ["*"],
    },
    zip_safe=False,
    keywords=[
        "pdf", "editor", "converter", "merge", "split", "compress",
        "security", "signature", "arabic", "gui", "pyqt5"
    ],
    project_urls={
        "Bug Reports": "https://github.com/your-username/pdf-editor-pro/issues",
        "Source": "https://github.com/your-username/pdf-editor-pro",
        "Documentation": "https://github.com/your-username/pdf-editor-pro/wiki",
    },
)
