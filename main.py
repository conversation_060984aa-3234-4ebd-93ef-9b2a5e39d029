#!/usr/bin/env python3
"""
PDF Editor Pro - تطبيق ذكي لتحرير ملفات PDF
Smart PDF Editor Application

المطور: PDF Editor Team
الإصدار: 1.0.0
"""

import sys
import os
from pathlib import Path

# إضافة مسار المشروع إلى sys.path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from PyQt5.QtWidgets import QApplication, QMessageBox, QSplashScreen
from PyQt5.QtCore import Qt, QTimer, QTranslator, QLocale
from PyQt5.QtGui import QPixmap, QFont, QFontDatabase
import qtawesome as qta

# استيراد وحدات التطبيق
from config.settings import AppSettings
from config.themes import theme_manager
from ui.main_window import MainWindow

class PDFEditorApp(QApplication):
    """فئة التطبيق الرئيسية"""
    
    def __init__(self, argv):
        super().__init__(argv)
        
        # إعداد التطبيق الأساسي
        self.setup_application()
        
        # إنشاء المجلدات المطلوبة
        AppSettings.ensure_directories()
        
        # إعداد الخطوط
        self.setup_fonts()
        
        # إعداد الترجمة
        self.setup_translation()
        
        # إنشاء شاشة البداية
        self.splash = self.create_splash_screen()
        
        # إنشاء النافذة الرئيسية
        self.main_window = None
        
        # تأخير إنشاء النافذة الرئيسية
        QTimer.singleShot(2000, self.show_main_window)
    
    def setup_application(self):
        """إعداد خصائص التطبيق الأساسية"""
        self.setApplicationName(AppSettings.APP_NAME)
        self.setApplicationVersion(AppSettings.APP_VERSION)
        self.setOrganizationName(AppSettings.APP_AUTHOR)
        
        # إعداد أيقونة التطبيق
        self.setWindowIcon(qta.icon('fa5s.file-pdf', color='#2196F3'))
        
        # إعداد نمط التطبيق
        self.setStyle('Fusion')
        
        # تمكين DPI العالي
        self.setAttribute(Qt.AA_EnableHighDpiScaling, True)
        self.setAttribute(Qt.AA_UseHighDpiPixmaps, True)
    
    def setup_fonts(self):
        """إعداد الخطوط"""
        # تحميل خطوط مخصصة إذا كانت متوفرة
        fonts_dir = AppSettings.RESOURCES_DIR / "fonts"
        if fonts_dir.exists():
            for font_file in fonts_dir.glob("*.ttf"):
                QFontDatabase.addApplicationFont(str(font_file))
        
        # تعيين الخط الافتراضي
        font = QFont("Segoe UI", 10)
        self.setFont(font)
    
    def setup_translation(self):
        """إعداد الترجمة"""
        self.translator = QTranslator()
        
        # تحديد اللغة بناءً على إعدادات النظام
        locale = QLocale.system().name()
        
        # تحميل ملف الترجمة إذا كان متوفراً
        translation_file = AppSettings.TRANSLATIONS_DIR / f"{locale}.qm"
        if translation_file.exists():
            self.translator.load(str(translation_file))
            self.installTranslator(self.translator)
    
    def create_splash_screen(self):
        """إنشاء شاشة البداية"""
        # إنشاء صورة شاشة البداية
        splash_pixmap = QPixmap(400, 300)
        splash_pixmap.fill(Qt.white)
        
        splash = QSplashScreen(splash_pixmap)
        splash.setWindowFlags(Qt.WindowStaysOnTopHint | Qt.SplashScreen)
        
        # إضافة نص لشاشة البداية
        splash.showMessage(
            f"تحميل {AppSettings.APP_NAME}...",
            Qt.AlignBottom | Qt.AlignCenter,
            Qt.black
        )
        
        splash.show()
        self.processEvents()
        
        return splash
    
    def show_main_window(self):
        """عرض النافذة الرئيسية"""
        try:
            # إنشاء النافذة الرئيسية
            self.main_window = MainWindow()
            
            # إخفاء شاشة البداية
            self.splash.finish(self.main_window)
            
            # عرض النافذة الرئيسية
            self.main_window.show()
            
        except Exception as e:
            # عرض رسالة خطأ في حالة فشل تحميل التطبيق
            QMessageBox.critical(
                None,
                "خطأ في التحميل",
                f"حدث خطأ أثناء تحميل التطبيق:\n{str(e)}"
            )
            self.quit()
    
    def handle_exception(self, exc_type, exc_value, exc_traceback):
        """معالج الأخطاء العامة"""
        if issubclass(exc_type, KeyboardInterrupt):
            sys.__excepthook__(exc_type, exc_value, exc_traceback)
            return
        
        error_msg = f"خطأ غير متوقع:\n{exc_type.__name__}: {exc_value}"
        
        QMessageBox.critical(
            None,
            "خطأ في التطبيق",
            error_msg
        )
        
        # طباعة تفاصيل الخطأ في وحدة التحكم
        sys.__excepthook__(exc_type, exc_value, exc_traceback)

def check_dependencies():
    """التحقق من توفر المكتبات المطلوبة"""
    required_modules = [
        'PyQt5',
        'qtawesome',
        'fitz',  # PyMuPDF
        'PyPDF2',
        'reportlab',
        'PIL',  # Pillow
        'docx2pdf',
        'pdf2docx',
        'openpyxl',
        'pptx',
        'img2pdf',
        'pdf2image',
        'pikepdf'
    ]
    
    missing_modules = []
    
    for module in required_modules:
        try:
            __import__(module)
        except ImportError:
            missing_modules.append(module)
    
    if missing_modules:
        print("المكتبات التالية مفقودة:")
        for module in missing_modules:
            print(f"  - {module}")
        print("\nيرجى تثبيت المكتبات المطلوبة باستخدام:")
        print("pip install -r requirements.txt")
        return False
    
    return True

def main():
    """الدالة الرئيسية للتطبيق"""
    # التحقق من المكتبات المطلوبة
    if not check_dependencies():
        sys.exit(1)
    
    # إنشاء التطبيق
    app = PDFEditorApp(sys.argv)
    
    # تعيين معالج الأخطاء
    sys.excepthook = app.handle_exception
    
    # تشغيل التطبيق
    try:
        sys.exit(app.exec_())
    except SystemExit:
        pass
    except Exception as e:
        QMessageBox.critical(
            None,
            "خطأ فادح",
            f"حدث خطأ فادح في التطبيق:\n{str(e)}"
        )
        sys.exit(1)

if __name__ == "__main__":
    main()
