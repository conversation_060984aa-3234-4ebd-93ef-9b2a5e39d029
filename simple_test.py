#!/usr/bin/env python3
"""
اختبار مبسط لتطبيق PDF Editor Pro
Simple test for PDF Editor Pro
"""

import sys
from pathlib import Path

# إضافة مسار المشروع
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_basic_imports():
    """اختبار الاستيراد الأساسي"""
    print("🧪 اختبار الاستيراد الأساسي...")
    print("🧪 Testing basic imports...")
    
    try:
        # اختبار PyQt5
        from PyQt5.QtWidgets import QApplication, QMainWindow
        print("✅ PyQt5")
        
        # اختبار الإعدادات
        from config.settings import AppSettings
        print("✅ config.settings")
        
        # اختبار الوحدات الأساسية (بدون مكتبات خارجية)
        print("✅ جميع الاستيرادات الأساسية نجحت!")
        print("✅ All basic imports successful!")
        return True
        
    except ImportError as e:
        print(f"❌ خطأ في الاستيراد: {e}")
        print(f"❌ Import error: {e}")
        return False

def test_basic_functionality():
    """اختبار الوظائف الأساسية"""
    print("\n🔧 اختبار الوظائف الأساسية...")
    print("🔧 Testing basic functionality...")
    
    try:
        from config.settings import AppSettings
        
        # اختبار الإعدادات
        assert AppSettings.APP_NAME == "PDF Editor Pro"
        assert AppSettings.WINDOW_WIDTH > 0
        assert AppSettings.WINDOW_HEIGHT > 0
        
        print("✅ الإعدادات تعمل بشكل صحيح!")
        print("✅ Settings working correctly!")
        
        # اختبار إنشاء المجلدات
        AppSettings.ensure_directories()
        print("✅ تم إنشاء المجلدات!")
        print("✅ Directories created!")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الوظائف: {e}")
        print(f"❌ Functionality error: {e}")
        return False

def test_app_creation():
    """اختبار إنشاء التطبيق الأساسي"""
    print("\n🚀 اختبار إنشاء التطبيق الأساسي...")
    print("🚀 Testing basic app creation...")
    
    try:
        from PyQt5.QtWidgets import QApplication, QMainWindow, QLabel
        from PyQt5.QtCore import Qt
        
        # إنشاء تطبيق مؤقت
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # إنشاء نافذة بسيطة
        window = QMainWindow()
        window.setWindowTitle("PDF Editor Pro - Test")
        window.resize(400, 300)
        
        # إضافة تسمية
        label = QLabel("PDF Editor Pro\nاختبار أساسي\nBasic Test")
        label.setAlignment(Qt.AlignCenter)
        window.setCentralWidget(label)
        
        print("✅ تم إنشاء النافذة الأساسية!")
        print("✅ Basic window created!")
        
        # تنظيف
        window.close()
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء التطبيق: {e}")
        print(f"❌ App creation error: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🧪 PDF Editor Pro - اختبار مبسط")
    print("🧪 PDF Editor Pro - Simple Test")
    print("=" * 40)
    
    tests = [
        ("الاستيراد الأساسي", "Basic Imports", test_basic_imports),
        ("الوظائف الأساسية", "Basic Functionality", test_basic_functionality),
        ("إنشاء التطبيق", "App Creation", test_app_creation),
    ]
    
    passed = 0
    total = len(tests)
    
    for ar_name, en_name, test_func in tests:
        try:
            if test_func():
                passed += 1
            else:
                print(f"\n❌ فشل اختبار: {ar_name}")
                print(f"❌ Test failed: {en_name}")
        except Exception as e:
            print(f"\n💥 خطأ في اختبار {ar_name}: {e}")
            print(f"💥 Error in test {en_name}: {e}")
    
    print("\n" + "=" * 40)
    print(f"📊 نتائج الاختبار: {passed}/{total}")
    print(f"📊 Test Results: {passed}/{total}")
    
    if passed == total:
        print("🎉 جميع الاختبارات الأساسية نجحت!")
        print("🎉 All basic tests passed!")
        print("\n💡 لتشغيل التطبيق الكامل:")
        print("💡 To run the full application:")
        print("1. pip install -r requirements.txt")
        print("2. python main.py")
        return True
    else:
        print("⚠️ بعض الاختبارات فشلت.")
        print("⚠️ Some tests failed.")
        return False

if __name__ == "__main__":
    success = main()
    
    print("\n" + "=" * 40)
    if success:
        print("✅ الهيكل الأساسي للتطبيق يعمل!")
        print("✅ Basic application structure works!")
    else:
        print("❌ يرجى مراجعة الأخطاء")
        print("❌ Please review the errors")
    
    input("\nاضغط Enter للخروج / Press Enter to exit...")
