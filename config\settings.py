"""
إعدادات التطبيق الرئيسية
Application Settings Configuration
"""

import os
from pathlib import Path

class AppSettings:
    """إعدادات التطبيق الأساسية"""
    
    # معلومات التطبيق
    APP_NAME = "PDF Editor Pro"
    APP_VERSION = "1.0.0"
    APP_AUTHOR = "PDF Editor Team"
    
    # المجلدات
    BASE_DIR = Path(__file__).parent.parent
    RESOURCES_DIR = BASE_DIR / "resources"
    ICONS_DIR = RESOURCES_DIR / "icons"
    STYLES_DIR = RESOURCES_DIR / "styles"
    TRANSLATIONS_DIR = RESOURCES_DIR / "translations"
    
    # إعدادات النافذة
    WINDOW_WIDTH = 1200
    WINDOW_HEIGHT = 800
    WINDOW_MIN_WIDTH = 800
    WINDOW_MIN_HEIGHT = 600
    
    # إعدادات الواجهة
    SIDEBAR_WIDTH = 250
    TOOLBAR_HEIGHT = 60
    STATUSBAR_HEIGHT = 30
    
    # الألوان الأساسية
    PRIMARY_COLOR = "#2196F3"
    SECONDARY_COLOR = "#FFC107"
    SUCCESS_COLOR = "#4CAF50"
    WARNING_COLOR = "#FF9800"
    ERROR_COLOR = "#F44336"
    
    # إعدادات PDF
    PDF_DEFAULT_DPI = 300
    MAX_FILE_SIZE_MB = 100
    SUPPORTED_FORMATS = [
        '.pdf', '.docx', '.doc', '.xlsx', '.xls', 
        '.pptx', '.ppt', '.jpg', '.jpeg', '.png', '.bmp'
    ]
    
    # إعدادات اللغة
    DEFAULT_LANGUAGE = "ar"  # العربية كلغة افتراضية
    SUPPORTED_LANGUAGES = ["ar", "en"]
    
    # مسارات الملفات المؤقتة
    TEMP_DIR = Path.home() / "PDFEditor" / "temp"
    OUTPUT_DIR = Path.home() / "PDFEditor" / "output"
    
    @classmethod
    def ensure_directories(cls):
        """إنشاء المجلدات المطلوبة إذا لم تكن موجودة"""
        cls.TEMP_DIR.mkdir(parents=True, exist_ok=True)
        cls.OUTPUT_DIR.mkdir(parents=True, exist_ok=True)
        cls.RESOURCES_DIR.mkdir(parents=True, exist_ok=True)
        cls.ICONS_DIR.mkdir(parents=True, exist_ok=True)
        cls.STYLES_DIR.mkdir(parents=True, exist_ok=True)
        cls.TRANSLATIONS_DIR.mkdir(parents=True, exist_ok=True)

class UISettings:
    """إعدادات الواجهة الرسومية"""


class UISettings:
    """إعدادات الواجهة الرسومية"""

    # إعدادات الواجهة
    SIDEBAR_WIDTH = 250
    TOOLBAR_HEIGHT = 60
    STATUSBAR_HEIGHT = 30

    # تأثيرات الحركة
    ANIMATION_DURATION = 300
    FADE_DURATION = 200
    SLIDE_DURATION = 250

    # الخطوط
    FONT_FAMILY = "Segoe UI"
    FONT_SIZE_SMALL = 9
    FONT_SIZE_NORMAL = 11
    FONT_SIZE_LARGE = 13
    FONT_SIZE_TITLE = 16

    # الأيقونات
    ICON_SIZE_SMALL = 16
    ICON_SIZE_NORMAL = 24
    ICON_SIZE_LARGE = 32
    ICON_SIZE_XLARGE = 48

    # التباعد
    SPACING_SMALL = 5
    SPACING_NORMAL = 10
    SPACING_LARGE = 20

    # الحواف
    BORDER_RADIUS = 8
    BORDER_WIDTH = 1

    # الظلال
    SHADOW_BLUR_RADIUS = 15
    SHADOW_OFFSET_X = 0
    SHADOW_OFFSET_Y = 5

class ProcessingSettings:
    """إعدادات معالجة الملفات"""
    
    # إعدادات الضغط
    COMPRESSION_QUALITY = 85
    IMAGE_QUALITY = 90

    # إعدادات التحويل
    CONVERSION_DPI = 300
    DEFAULT_DPI = 300
    OCR_LANGUAGE = "ara+eng"  # العربية والإنجليزية
    
    # إعدادات الأمان
    DEFAULT_PASSWORD_LENGTH = 12
    ENCRYPTION_LEVEL = 128  # 128-bit AES
    
    # حدود المعالجة
    MAX_PAGES_PER_FILE = 1000
    MAX_CONCURRENT_OPERATIONS = 3
    CHUNK_SIZE = 1024 * 1024  # 1MB chunks
