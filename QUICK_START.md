# 🚀 البدء السريع - PDF Editor Pro
# 🚀 Quick Start - PDF Editor Pro

## 📋 نظرة سريعة - Quick Overview

**PDF Editor Pro** هو تطبيق ذكي وعصري لتحرير ملفات PDF بواجهة رسومية متقدمة.

**PDF Editor Pro** is a smart and modern PDF editing application with advanced GUI.

## ⚡ التشغيل السريع - Quick Run

### الطريقة الأولى - Method 1: Basic Test
```bash
# اختبار أساسي بدون تثبيت مكتبات إضافية
# Basic test without installing additional libraries
python simple_test.py
```

### الطريقة الثانية - Method 2: Full Installation
```bash
# تثبيت جميع المكتبات
# Install all libraries
pip install -r requirements.txt

# تشغيل التطبيق الكامل
# Run full application
python main.py
```

### الطريقة الثالثة - Method 3: Using Scripts

#### Windows:
```cmd
run.bat
```

#### Linux/macOS:
```bash
chmod +x run.sh
./run.sh
```

## 📦 المتطلبات الأساسية - Basic Requirements

### ضروري - Essential
- **Python 3.7+**
- **PyQt5** (للواجهة الرسومية)

### للتشغيل الكامل - For Full Functionality
- جميع المكتبات في `requirements.txt`
- All libraries in `requirements.txt`

## 🛠️ التثبيت التدريجي - Step-by-Step Installation

### 1. التحقق من Python
```bash
python --version
# يجب أن يكون 3.7 أو أحدث
# Should be 3.7 or newer
```

### 2. تثبيت المكتبات الأساسية
```bash
# المكتبات الأساسية فقط
# Basic libraries only
pip install PyQt5 PyMuPDF PyPDF2 Pillow

# أو تثبيت من الملف
# Or install from file
pip install -r requirements_basic.txt
```

### 3. اختبار التشغيل
```bash
# اختبار بسيط
# Simple test
python simple_test.py

# اختبار شامل (يتطلب جميع المكتبات)
# Comprehensive test (requires all libraries)
python test_app.py
```

### 4. تشغيل التطبيق
```bash
# تشغيل مباشر
# Direct run
python main.py

# أو باستخدام المشغل
# Or using launcher
python run.py
```

## 🎯 الميزات الأساسية - Core Features

### ✅ متوفرة حالياً - Currently Available
- إنشاء ملفات PDF / Create PDF files
- دمج ملفات PDF / Merge PDF files  
- تقسيم ملفات PDF / Split PDF files
- ضغط ملفات PDF / Compress PDF files
- حماية بكلمة مرور / Password protection
- واجهة عربية عصرية / Modern Arabic interface

### 🔄 تحويل الملفات - File Conversion
- PDF ↔ Word (DOCX)
- PDF ↔ Excel (XLSX)
- PDF ↔ PowerPoint (PPTX)
- PDF ↔ Images (JPG, PNG)

## 🐛 حل المشاكل الشائعة - Troubleshooting

### مشكلة: "No module named 'qtawesome'"
```bash
pip install qtawesome
```

### مشكلة: "No module named 'PyPDF2'"
```bash
pip install PyPDF2
```

### مشكلة: "No module named 'fitz'"
```bash
pip install PyMuPDF
```

### تثبيت جميع المكتبات مرة واحدة
```bash
pip install -r requirements.txt
```

## 📁 هيكل المشروع - Project Structure

```
pdf_editor/
├── main.py              # التطبيق الرئيسي / Main app
├── run.py               # مشغل سريع / Quick launcher
├── simple_test.py       # اختبار بسيط / Simple test
├── requirements.txt     # جميع المكتبات / All libraries
├── requirements_basic.txt # المكتبات الأساسية / Basic libraries
├── config/              # الإعدادات / Settings
├── ui/                  # الواجهة / Interface
├── core/                # المنطق الأساسي / Core logic
└── resources/           # الموارد / Resources
```

## 🎨 لقطات الشاشة - Screenshots

### الواجهة الرئيسية - Main Interface
- شريط جانبي تفاعلي / Interactive sidebar
- ثيمات عصرية / Modern themes
- دعم اللغة العربية / Arabic language support

### الميزات - Features
- دمج الملفات / File merging
- تقسيم الملفات / File splitting
- تحويل الصيغ / Format conversion
- الحماية والأمان / Security & protection

## 📞 الدعم - Support

### للمساعدة السريعة - Quick Help
1. تأكد من تثبيت Python 3.7+
2. ثبت المكتبات الأساسية
3. شغل الاختبار البسيط
4. إذا نجح، ثبت باقي المكتبات

### للمشاكل المتقدمة - Advanced Issues
- راجع ملف `README.md` الكامل
- شغل `test_app.py` للتشخيص الشامل
- تحقق من ملف `PROJECT_SUMMARY.md`

## 🎉 نصائح للبدء - Getting Started Tips

1. **ابدأ بالاختبار البسيط** - Start with simple test
2. **ثبت المكتبات تدريجياً** - Install libraries gradually  
3. **استخدم الملفات المساعدة** - Use helper scripts
4. **راجع التوثيق** - Check documentation

---

## 🚀 جاهز للبدء؟ - Ready to Start?

```bash
# خطوة واحدة للتشغيل
# One step to run
python simple_test.py
```

**استمتع باستخدام PDF Editor Pro! 🎯**
**Enjoy using PDF Editor Pro! 🎯**
