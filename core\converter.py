"""
محول الملفات - تحويل بين صيغ مختلفة
File Converter - Convert between different formats
"""

import os
import fitz  # PyMuPDF
from pathlib import Path
from typing import List, Optional, Dict, Any
from PyQt5.QtCore import QObject, pyqtSignal, QThread
import docx2pdf
import pdf2docx
from openpyxl import Workbook
from openpyxl.drawing.image import Image as XLImage
import xlsxwriter
from pptx import Presentation
from pptx.util import Inches
from PIL import Image
import img2pdf
from pdf2image import convert_from_path
import tempfile
import shutil

from config.settings import ProcessingSettings

class ConversionOperation(QThread):
    """خيط تحويل الملفات"""
    
    progress_updated = pyqtSignal(int)
    operation_completed = pyqtSignal(bool, str)
    status_updated = pyqtSignal(str)
    
    def __init__(self, conversion_type, input_file, output_file, **kwargs):
        super().__init__()
        self.conversion_type = conversion_type
        self.input_file = input_file
        self.output_file = output_file
        self.kwargs = kwargs
        self.is_cancelled = False
    
    def run(self):
        """تشغيل عملية التحويل"""
        try:
            if self.conversion_type == "pdf_to_word":
                self.pdf_to_word()
            elif self.conversion_type == "word_to_pdf":
                self.word_to_pdf()
            elif self.conversion_type == "pdf_to_excel":
                self.pdf_to_excel()
            elif self.conversion_type == "pdf_to_ppt":
                self.pdf_to_ppt()
            elif self.conversion_type == "images_to_pdf":
                self.images_to_pdf()
            elif self.conversion_type == "pdf_to_images":
                self.pdf_to_images()
            else:
                self.operation_completed.emit(False, "نوع التحويل غير مدعوم")
        except Exception as e:
            self.operation_completed.emit(False, f"خطأ في التحويل: {str(e)}")
    
    def pdf_to_word(self):
        """تحويل PDF إلى Word"""
        self.status_updated.emit("بدء تحويل PDF إلى Word...")
        
        try:
            # استخدام pdf2docx
            cv = pdf2docx.Converter(self.input_file)
            cv.convert(self.output_file, start=0, end=None)
            cv.close()
            
            self.progress_updated.emit(100)
            self.operation_completed.emit(True, "تم تحويل PDF إلى Word بنجاح")
            
        except Exception as e:
            self.operation_completed.emit(False, f"خطأ في تحويل PDF إلى Word: {str(e)}")
    
    def word_to_pdf(self):
        """تحويل Word إلى PDF"""
        self.status_updated.emit("بدء تحويل Word إلى PDF...")
        
        try:
            # استخدام docx2pdf
            docx2pdf.convert(self.input_file, self.output_file)
            
            self.progress_updated.emit(100)
            self.operation_completed.emit(True, "تم تحويل Word إلى PDF بنجاح")
            
        except Exception as e:
            self.operation_completed.emit(False, f"خطأ في تحويل Word إلى PDF: {str(e)}")
    
    def pdf_to_excel(self):
        """تحويل PDF إلى Excel"""
        self.status_updated.emit("بدء تحويل PDF إلى Excel...")
        
        try:
            # فتح ملف PDF واستخراج النص
            doc = fitz.open(self.input_file)
            workbook = Workbook()
            worksheet = workbook.active
            worksheet.title = "PDF Content"
            
            total_pages = len(doc)
            current_row = 1
            
            for page_num in range(total_pages):
                if self.is_cancelled:
                    break
                
                page = doc[page_num]
                text = page.get_text()
                
                # تقسيم النص إلى أسطر وإضافتها للإكسل
                lines = text.split('\n')
                for line in lines:
                    if line.strip():
                        worksheet.cell(row=current_row, column=1, value=line.strip())
                        current_row += 1
                
                progress = int((page_num + 1) / total_pages * 100)
                self.progress_updated.emit(progress)
                self.status_updated.emit(f"معالجة الصفحة {page_num + 1} من {total_pages}")
            
            if not self.is_cancelled:
                workbook.save(self.output_file)
                doc.close()
                self.operation_completed.emit(True, "تم تحويل PDF إلى Excel بنجاح")
            else:
                doc.close()
                self.operation_completed.emit(False, "تم إلغاء العملية")
                
        except Exception as e:
            self.operation_completed.emit(False, f"خطأ في تحويل PDF إلى Excel: {str(e)}")
    
    def pdf_to_ppt(self):
        """تحويل PDF إلى PowerPoint"""
        self.status_updated.emit("بدء تحويل PDF إلى PowerPoint...")
        
        try:
            # تحويل PDF إلى صور أولاً
            temp_dir = tempfile.mkdtemp()
            images = convert_from_path(self.input_file, dpi=ProcessingSettings.CONVERSION_DPI)
            
            # إنشاء عرض PowerPoint
            prs = Presentation()
            total_images = len(images)
            
            for i, image in enumerate(images):
                if self.is_cancelled:
                    break
                
                # حفظ الصورة مؤقتاً
                img_path = os.path.join(temp_dir, f"slide_{i+1}.png")
                image.save(img_path, "PNG")
                
                # إضافة شريحة جديدة
                slide_layout = prs.slide_layouts[6]  # Blank layout
                slide = prs.slides.add_slide(slide_layout)
                
                # إضافة الصورة للشريحة
                slide.shapes.add_picture(img_path, Inches(0), Inches(0), 
                                       width=Inches(10), height=Inches(7.5))
                
                progress = int((i + 1) / total_images * 100)
                self.progress_updated.emit(progress)
                self.status_updated.emit(f"معالجة الشريحة {i + 1} من {total_images}")
            
            if not self.is_cancelled:
                prs.save(self.output_file)
                shutil.rmtree(temp_dir)
                self.operation_completed.emit(True, f"تم تحويل PDF إلى PowerPoint ({total_images} شريحة)")
            else:
                shutil.rmtree(temp_dir)
                self.operation_completed.emit(False, "تم إلغاء العملية")
                
        except Exception as e:
            self.operation_completed.emit(False, f"خطأ في تحويل PDF إلى PowerPoint: {str(e)}")
    
    def images_to_pdf(self):
        """تحويل الصور إلى PDF"""
        self.status_updated.emit("بدء تحويل الصور إلى PDF...")
        
        try:
            image_files = self.kwargs.get('image_files', [])
            if not image_files:
                self.operation_completed.emit(False, "لا توجد صور للتحويل")
                return
            
            # تحويل الصور إلى PDF
            with open(self.output_file, "wb") as f:
                f.write(img2pdf.convert(image_files))
            
            self.progress_updated.emit(100)
            self.operation_completed.emit(True, f"تم تحويل {len(image_files)} صورة إلى PDF")
            
        except Exception as e:
            self.operation_completed.emit(False, f"خطأ في تحويل الصور إلى PDF: {str(e)}")
    
    def pdf_to_images(self):
        """تحويل PDF إلى صور"""
        self.status_updated.emit("بدء تحويل PDF إلى صور...")
        
        try:
            output_dir = self.kwargs.get('output_dir', os.path.dirname(self.output_file))
            image_format = self.kwargs.get('format', 'PNG')
            dpi = self.kwargs.get('dpi', ProcessingSettings.CONVERSION_DPI)
            
            # تحويل PDF إلى صور
            images = convert_from_path(self.input_file, dpi=dpi)
            total_images = len(images)
            saved_files = []
            
            for i, image in enumerate(images):
                if self.is_cancelled:
                    break
                
                filename = f"page_{i+1}.{image_format.lower()}"
                filepath = os.path.join(output_dir, filename)
                image.save(filepath, image_format)
                saved_files.append(filepath)
                
                progress = int((i + 1) / total_images * 100)
                self.progress_updated.emit(progress)
                self.status_updated.emit(f"حفظ الصورة {i + 1} من {total_images}")
            
            if not self.is_cancelled:
                self.operation_completed.emit(True, f"تم تحويل PDF إلى {len(saved_files)} صورة")
            else:
                self.operation_completed.emit(False, "تم إلغاء العملية")
                
        except Exception as e:
            self.operation_completed.emit(False, f"خطأ في تحويل PDF إلى صور: {str(e)}")
    
    def cancel(self):
        """إلغاء العملية"""
        self.is_cancelled = True

class FileConverter(QObject):
    """محول الملفات الرئيسي"""
    
    def __init__(self):
        super().__init__()
        self.current_operation = None
    
    def start_conversion(self, conversion_type: str, input_file: str, 
                        output_file: str, **kwargs) -> ConversionOperation:
        """بدء عملية تحويل جديدة"""
        if self.current_operation and self.current_operation.isRunning():
            self.current_operation.cancel()
            self.current_operation.wait()
        
        self.current_operation = ConversionOperation(
            conversion_type, input_file, output_file, **kwargs
        )
        return self.current_operation
    
    def cancel_current_conversion(self):
        """إلغاء التحويل الحالي"""
        if self.current_operation and self.current_operation.isRunning():
            self.current_operation.cancel()
    
    def get_supported_formats(self) -> Dict[str, List[str]]:
        """الحصول على الصيغ المدعومة"""
        return {
            'input': ['.pdf', '.docx', '.doc', '.jpg', '.jpeg', '.png', '.bmp'],
            'output': ['.pdf', '.docx', '.xlsx', '.pptx', '.jpg', '.png']
        }
    
    def is_conversion_supported(self, input_format: str, output_format: str) -> bool:
        """التحقق من دعم التحويل"""
        supported_conversions = {
            '.pdf': ['.docx', '.xlsx', '.pptx', '.jpg', '.png'],
            '.docx': ['.pdf'],
            '.doc': ['.pdf'],
            '.jpg': ['.pdf'],
            '.jpeg': ['.pdf'],
            '.png': ['.pdf'],
            '.bmp': ['.pdf']
        }
        
        return output_format in supported_conversions.get(input_format, [])
