"""
النافذة الرئيسية للتطبيق
Main Application Window
"""

import sys
import os
from PyQt5.QtWidgets import (QMainWindow, QWidget, QHBoxLayout, QVBoxLayout, 
                             QStackedWidget, QLabel, QFrame, QSplitter,
                             QMenuBar, QStatusBar, QToolBar, QAction,
                             QFileDialog, QMessageBox, QProgressBar,
                             QApplication)
from PyQt5.QtCore import Qt, pyqtSignal, QTimer, QPropertyAnimation, QEasingCurve
from PyQt5.QtGui import QIcon, QFont, QPixmap
import qtawesome as qta

from config.settings import AppSettings, UISettings
from config.themes import theme_manager
from ui.components.sidebar import Sidebar
from core.pdf_manager import PDFManager
from core.converter import FileConverter

class WelcomeWidget(QWidget):
    """شاشة الترحيب الرئيسية"""
    
    file_dropped = pyqtSignal(str)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setAcceptDrops(True)
        self.setup_ui()
    
    def setup_ui(self):
        """إعداد واجهة شاشة الترحيب"""
        layout = QVBoxLayout(self)
        layout.setAlignment(Qt.AlignCenter)
        layout.setSpacing(30)
        
        # أيقونة كبيرة
        icon_label = QLabel()
        icon_pixmap = qta.icon('fa5s.file-pdf', color='#2196F3').pixmap(128, 128)
        icon_label.setPixmap(icon_pixmap)
        icon_label.setAlignment(Qt.AlignCenter)
        
        # عنوان الترحيب
        title_label = QLabel("مرحباً بك في PDF Editor Pro")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setFont(QFont("Segoe UI", 24, QFont.Bold))
        title_label.setStyleSheet("color: #2196F3; margin: 20px;")
        
        # وصف
        desc_label = QLabel("اختر ميزة من الشريط الجانبي أو اسحب ملف PDF هنا للبدء")
        desc_label.setAlignment(Qt.AlignCenter)
        desc_label.setFont(QFont("Segoe UI", 14))
        desc_label.setStyleSheet("color: #666; margin: 10px;")
        
        # منطقة السحب والإفلات
        drop_area = QFrame()
        drop_area.setFrameStyle(QFrame.StyledPanel)
        drop_area.setStyleSheet("""
            QFrame {
                border: 2px dashed #CCCCCC;
                border-radius: 10px;
                background-color: #FAFAFA;
                min-height: 200px;
            }
        """)
        
        drop_layout = QVBoxLayout(drop_area)
        drop_layout.setAlignment(Qt.AlignCenter)
        
        drop_icon = QLabel()
        drop_icon.setPixmap(qta.icon('fa5s.cloud-upload-alt', color='#CCCCCC').pixmap(64, 64))
        drop_icon.setAlignment(Qt.AlignCenter)
        
        drop_text = QLabel("اسحب ملفات PDF هنا")
        drop_text.setAlignment(Qt.AlignCenter)
        drop_text.setFont(QFont("Segoe UI", 12))
        drop_text.setStyleSheet("color: #999;")
        
        drop_layout.addWidget(drop_icon)
        drop_layout.addWidget(drop_text)
        
        # إضافة العناصر للتخطيط الرئيسي
        layout.addWidget(icon_label)
        layout.addWidget(title_label)
        layout.addWidget(desc_label)
        layout.addWidget(drop_area)
    
    def dragEnterEvent(self, event):
        """معالج دخول السحب"""
        if event.mimeData().hasUrls():
            event.accept()
        else:
            event.ignore()
    
    def dropEvent(self, event):
        """معالج الإفلات"""
        files = [u.toLocalFile() for u in event.mimeData().urls()]
        for file_path in files:
            if file_path.lower().endswith('.pdf'):
                self.file_dropped.emit(file_path)
                break

class MainWindow(QMainWindow):
    """النافذة الرئيسية للتطبيق"""
    
    def __init__(self):
        super().__init__()
        self.pdf_manager = PDFManager()
        self.file_converter = FileConverter()
        self.current_file = None
        
        self.setup_ui()
        self.setup_connections()
        self.setup_animations()
        
        # تطبيق الثيم
        theme_manager.apply_theme(self)
        
        # إعداد النافذة
        self.setWindowTitle(AppSettings.APP_NAME)
        self.setWindowIcon(qta.icon('fa5s.file-pdf'))
        self.resize(AppSettings.WINDOW_WIDTH, AppSettings.WINDOW_HEIGHT)
        self.setMinimumSize(AppSettings.WINDOW_MIN_WIDTH, AppSettings.WINDOW_MIN_HEIGHT)
        
        # توسيط النافذة
        self.center_window()
    
    def setup_ui(self):
        """إعداد واجهة النافذة الرئيسية"""
        # إنشاء القائمة الرئيسية
        self.create_menu_bar()
        
        # إنشاء شريط الأدوات
        self.create_toolbar()
        
        # إنشاء شريط الحالة
        self.create_status_bar()
        
        # إنشاء المحتوى الرئيسي
        self.create_main_content()
    
    def create_menu_bar(self):
        """إنشاء شريط القوائم"""
        menubar = self.menuBar()
        
        # قائمة الملف
        file_menu = menubar.addMenu('ملف')
        
        open_action = QAction(qta.icon('fa5s.folder-open'), 'فتح', self)
        open_action.setShortcut('Ctrl+O')
        open_action.triggered.connect(self.open_file)
        file_menu.addAction(open_action)
        
        file_menu.addSeparator()
        
        exit_action = QAction(qta.icon('fa5s.times'), 'خروج', self)
        exit_action.setShortcut('Ctrl+Q')
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # قائمة العرض
        view_menu = menubar.addMenu('عرض')
        
        toggle_sidebar_action = QAction(qta.icon('fa5s.bars'), 'إظهار/إخفاء الشريط الجانبي', self)
        toggle_sidebar_action.setShortcut('Ctrl+B')
        toggle_sidebar_action.triggered.connect(self.toggle_sidebar)
        view_menu.addAction(toggle_sidebar_action)
        
        toggle_theme_action = QAction(qta.icon('fa5s.moon'), 'تبديل الثيم', self)
        toggle_theme_action.setShortcut('Ctrl+T')
        toggle_theme_action.triggered.connect(self.toggle_theme)
        view_menu.addAction(toggle_theme_action)
        
        # قائمة المساعدة
        help_menu = menubar.addMenu('مساعدة')
        
        about_action = QAction(qta.icon('fa5s.info-circle'), 'حول', self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)
    
    def create_toolbar(self):
        """إنشاء شريط الأدوات"""
        toolbar = self.addToolBar('الأدوات الرئيسية')
        toolbar.setMovable(False)
        toolbar.setFloatable(False)
        
        # زر فتح ملف
        open_action = QAction(qta.icon('fa5s.folder-open'), 'فتح ملف', self)
        open_action.triggered.connect(self.open_file)
        toolbar.addAction(open_action)
        
        toolbar.addSeparator()
        
        # زر دمج الملفات
        merge_action = QAction(qta.icon('fa5s.layer-group'), 'دمج ملفات', self)
        merge_action.triggered.connect(lambda: self.sidebar.select_feature('merge'))
        toolbar.addAction(merge_action)
        
        # زر تقسيم الملف
        split_action = QAction(qta.icon('fa5s.cut'), 'تقسيم ملف', self)
        split_action.triggered.connect(lambda: self.sidebar.select_feature('split'))
        toolbar.addAction(split_action)
        
        # زر ضغط الملف
        compress_action = QAction(qta.icon('fa5s.compress-arrows-alt'), 'ضغط ملف', self)
        compress_action.triggered.connect(lambda: self.sidebar.select_feature('compress'))
        toolbar.addAction(compress_action)
    
    def create_status_bar(self):
        """إنشاء شريط الحالة"""
        self.status_bar = self.statusBar()
        
        # تسمية الحالة
        self.status_label = QLabel("جاهز")
        self.status_bar.addWidget(self.status_label)
        
        # شريط التقدم
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.progress_bar.setMaximumWidth(200)
        self.status_bar.addPermanentWidget(self.progress_bar)
        
        # معلومات الملف الحالي
        self.file_info_label = QLabel("")
        self.status_bar.addPermanentWidget(self.file_info_label)
    
    def create_main_content(self):
        """إنشاء المحتوى الرئيسي"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # تخطيط أفقي رئيسي
        main_layout = QHBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # الشريط الجانبي
        self.sidebar = Sidebar()
        main_layout.addWidget(self.sidebar)
        
        # المحتوى الرئيسي
        self.content_stack = QStackedWidget()
        
        # شاشة الترحيب
        self.welcome_widget = WelcomeWidget()
        self.content_stack.addWidget(self.welcome_widget)
        
        main_layout.addWidget(self.content_stack)
    
    def setup_connections(self):
        """إعداد الاتصالات والإشارات"""
        # اتصال الشريط الجانبي
        self.sidebar.feature_selected.connect(self.on_feature_selected)
        
        # اتصال شاشة الترحيب
        self.welcome_widget.file_dropped.connect(self.load_file)
    
    def setup_animations(self):
        """إعداد الحركات والتأثيرات"""
        # حركة تلاشي للمحتوى
        self.fade_animation = QPropertyAnimation(self.content_stack, b"windowOpacity")
        self.fade_animation.setDuration(UISettings.FADE_DURATION)
        self.fade_animation.setEasingCurve(QEasingCurve.InOutQuad)
    
    def center_window(self):
        """توسيط النافذة على الشاشة"""
        screen = QApplication.desktop().screenGeometry()
        window = self.geometry()
        x = (screen.width() - window.width()) // 2
        y = (screen.height() - window.height()) // 2
        self.move(x, y)
    
    def on_feature_selected(self, feature_id):
        """معالج اختيار الميزة"""
        self.update_status(f"تم اختيار: {feature_id}")
        
        # هنا يمكن إضافة منطق التنقل بين الميزات المختلفة
        if feature_id == "settings":
            self.show_settings()
        elif feature_id == "help":
            self.show_help()
        else:
            # عرض واجهة الميزة المحددة
            self.show_feature_interface(feature_id)
    
    def show_feature_interface(self, feature_id):
        """عرض واجهة الميزة المحددة"""
        # مؤقتاً - سيتم تطوير واجهات منفصلة لكل ميزة
        feature_widget = QLabel(f"واجهة الميزة: {feature_id}")
        feature_widget.setAlignment(Qt.AlignCenter)
        feature_widget.setFont(QFont("Segoe UI", 16))
        
        # إضافة الواجهة للمكدس إذا لم تكن موجودة
        self.content_stack.addWidget(feature_widget)
        self.content_stack.setCurrentWidget(feature_widget)
    
    def open_file(self):
        """فتح ملف PDF"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "فتح ملف PDF", "", "PDF Files (*.pdf);;All Files (*)"
        )
        
        if file_path:
            self.load_file(file_path)
    
    def load_file(self, file_path):
        """تحميل ملف PDF"""
        if not os.path.exists(file_path):
            QMessageBox.warning(self, "خطأ", "الملف غير موجود")
            return
        
        if not self.pdf_manager.is_pdf_valid(file_path):
            QMessageBox.warning(self, "خطأ", "الملف ليس ملف PDF صحيح")
            return
        
        self.current_file = file_path
        file_info = self.pdf_manager.get_pdf_info(file_path)
        
        if 'error' in file_info:
            QMessageBox.warning(self, "خطأ", f"خطأ في قراءة الملف: {file_info['error']}")
            return
        
        # تحديث معلومات الملف في شريط الحالة
        file_name = os.path.basename(file_path)
        pages_count = file_info.get('pages', 0)
        self.file_info_label.setText(f"{file_name} - {pages_count} صفحة")
        
        self.update_status(f"تم تحميل الملف: {file_name}")
    
    def toggle_sidebar(self):
        """إظهار/إخفاء الشريط الجانبي"""
        self.sidebar.toggle_sidebar()
    
    def toggle_theme(self):
        """تبديل الثيم"""
        theme_manager.toggle_theme()
        theme_manager.apply_theme(self)
    
    def show_settings(self):
        """عرض نافذة الإعدادات"""
        QMessageBox.information(self, "الإعدادات", "نافذة الإعدادات قيد التطوير")
    
    def show_help(self):
        """عرض نافذة المساعدة"""
        QMessageBox.information(self, "المساعدة", "نافذة المساعدة قيد التطوير")
    
    def show_about(self):
        """عرض نافذة حول التطبيق"""
        QMessageBox.about(self, "حول التطبيق", 
                         f"{AppSettings.APP_NAME} v{AppSettings.APP_VERSION}\n"
                         f"تطوير: {AppSettings.APP_AUTHOR}\n\n"
                         "محرر PDF ذكي وعصري لجميع احتياجاتك")
    
    def update_status(self, message):
        """تحديث رسالة الحالة"""
        self.status_label.setText(message)
        
        # إخفاء الرسالة بعد 3 ثوان
        QTimer.singleShot(3000, lambda: self.status_label.setText("جاهز"))
    
    def show_progress(self, show=True):
        """إظهار/إخفاء شريط التقدم"""
        self.progress_bar.setVisible(show)
        if not show:
            self.progress_bar.setValue(0)
    
    def update_progress(self, value):
        """تحديث قيمة التقدم"""
        self.progress_bar.setValue(value)
