"""
مدير ملفات PDF الأساسي
Core PDF Manager for handling PDF operations
"""

import os
import fitz  # PyMuPDF
import PyPDF2
from pathlib import Path
from typing import List, Optional, Dict, Any
from PyQt5.QtCore import QObject, pyqtSignal, QThread, QMutex
import pikepdf
from reportlab.pdfgen import canvas
from reportlab.lib.pagesizes import letter, A4
from reportlab.lib.utils import ImageReader
from PIL import Image
import tempfile
import shutil

from config.settings import ProcessingSettings, AppSettings

class PDFOperation(QThread):
    """خيط معالجة عمليات PDF"""
    
    progress_updated = pyqtSignal(int)  # تحديث التقدم
    operation_completed = pyqtSignal(bool, str)  # اكتمال العملية (نجح، رسالة)
    status_updated = pyqtSignal(str)  # تحديث الحالة
    
    def __init__(self, operation_type, **kwargs):
        super().__init__()
        self.operation_type = operation_type
        self.kwargs = kwargs
        self.is_cancelled = False
    
    def run(self):
        """تشغيل العملية"""
        try:
            if self.operation_type == "merge":
                self.merge_pdfs()
            elif self.operation_type == "split":
                self.split_pdf()
            elif self.operation_type == "compress":
                self.compress_pdf()
            elif self.operation_type == "create":
                self.create_pdf()
            else:
                self.operation_completed.emit(False, "عملية غير مدعومة")
        except Exception as e:
            self.operation_completed.emit(False, f"خطأ: {str(e)}")
    
    def merge_pdfs(self):
        """دمج ملفات PDF"""
        input_files = self.kwargs.get('input_files', [])
        output_file = self.kwargs.get('output_file', '')
        
        if not input_files or not output_file:
            self.operation_completed.emit(False, "ملفات الإدخال أو الإخراج غير محددة")
            return
        
        self.status_updated.emit("بدء دمج الملفات...")
        
        try:
            merger = PyPDF2.PdfMerger()
            total_files = len(input_files)
            
            for i, file_path in enumerate(input_files):
                if self.is_cancelled:
                    break
                
                self.status_updated.emit(f"معالجة الملف {i+1} من {total_files}")
                merger.append(file_path)
                
                progress = int((i + 1) / total_files * 100)
                self.progress_updated.emit(progress)
            
            if not self.is_cancelled:
                merger.write(output_file)
                merger.close()
                self.operation_completed.emit(True, f"تم دمج {total_files} ملف بنجاح")
            else:
                merger.close()
                self.operation_completed.emit(False, "تم إلغاء العملية")
                
        except Exception as e:
            self.operation_completed.emit(False, f"خطأ في دمج الملفات: {str(e)}")
    
    def split_pdf(self):
        """تقسيم ملف PDF"""
        input_file = self.kwargs.get('input_file', '')
        output_dir = self.kwargs.get('output_dir', '')
        split_type = self.kwargs.get('split_type', 'pages')  # pages, range, size
        
        if not input_file or not output_dir:
            self.operation_completed.emit(False, "ملف الإدخال أو مجلد الإخراج غير محدد")
            return
        
        self.status_updated.emit("بدء تقسيم الملف...")
        
        try:
            with open(input_file, 'rb') as file:
                reader = PyPDF2.PdfReader(file)
                total_pages = len(reader.pages)
                
                if split_type == 'pages':
                    # تقسيم كل صفحة في ملف منفصل
                    for i in range(total_pages):
                        if self.is_cancelled:
                            break
                        
                        writer = PyPDF2.PdfWriter()
                        writer.add_page(reader.pages[i])
                        
                        output_path = os.path.join(output_dir, f"page_{i+1}.pdf")
                        with open(output_path, 'wb') as output_file:
                            writer.write(output_file)
                        
                        progress = int((i + 1) / total_pages * 100)
                        self.progress_updated.emit(progress)
                        self.status_updated.emit(f"تقسيم الصفحة {i+1} من {total_pages}")
                
                if not self.is_cancelled:
                    self.operation_completed.emit(True, f"تم تقسيم الملف إلى {total_pages} صفحة")
                else:
                    self.operation_completed.emit(False, "تم إلغاء العملية")
                    
        except Exception as e:
            self.operation_completed.emit(False, f"خطأ في تقسيم الملف: {str(e)}")
    
    def compress_pdf(self):
        """ضغط ملف PDF"""
        input_file = self.kwargs.get('input_file', '')
        output_file = self.kwargs.get('output_file', '')
        quality = self.kwargs.get('quality', ProcessingSettings.COMPRESSION_QUALITY)
        
        if not input_file or not output_file:
            self.operation_completed.emit(False, "ملف الإدخال أو الإخراج غير محدد")
            return
        
        self.status_updated.emit("بدء ضغط الملف...")
        
        try:
            # استخدام PyMuPDF للضغط
            doc = fitz.open(input_file)
            total_pages = len(doc)
            
            for i in range(total_pages):
                if self.is_cancelled:
                    break
                
                page = doc[i]
                # ضغط الصور في الصفحة
                page.clean_contents()
                
                progress = int((i + 1) / total_pages * 100)
                self.progress_updated.emit(progress)
                self.status_updated.emit(f"ضغط الصفحة {i+1} من {total_pages}")
            
            if not self.is_cancelled:
                doc.save(output_file, garbage=4, deflate=True, clean=True)
                doc.close()
                
                # حساب نسبة الضغط
                original_size = os.path.getsize(input_file)
                compressed_size = os.path.getsize(output_file)
                compression_ratio = (1 - compressed_size / original_size) * 100
                
                self.operation_completed.emit(True, 
                    f"تم ضغط الملف بنجاح. نسبة الضغط: {compression_ratio:.1f}%")
            else:
                doc.close()
                self.operation_completed.emit(False, "تم إلغاء العملية")
                
        except Exception as e:
            self.operation_completed.emit(False, f"خطأ في ضغط الملف: {str(e)}")
    
    def create_pdf(self):
        """إنشاء ملف PDF جديد"""
        output_file = self.kwargs.get('output_file', '')
        content = self.kwargs.get('content', {})
        
        if not output_file:
            self.operation_completed.emit(False, "ملف الإخراج غير محدد")
            return
        
        self.status_updated.emit("إنشاء ملف PDF جديد...")
        
        try:
            c = canvas.Canvas(output_file, pagesize=A4)
            width, height = A4
            
            # إضافة النصوص
            texts = content.get('texts', [])
            for i, text_info in enumerate(texts):
                if self.is_cancelled:
                    break
                
                text = text_info.get('text', '')
                x = text_info.get('x', 100)
                y = text_info.get('y', height - 100)
                font_size = text_info.get('font_size', 12)
                
                c.setFont("Helvetica", font_size)
                c.drawString(x, y, text)
                
                progress = int((i + 1) / len(texts) * 50)
                self.progress_updated.emit(progress)
            
            # إضافة الصور
            images = content.get('images', [])
            for i, image_info in enumerate(images):
                if self.is_cancelled:
                    break
                
                image_path = image_info.get('path', '')
                x = image_info.get('x', 100)
                y = image_info.get('y', height - 300)
                width_img = image_info.get('width', 200)
                height_img = image_info.get('height', 200)
                
                if os.path.exists(image_path):
                    c.drawImage(image_path, x, y, width_img, height_img)
                
                progress = 50 + int((i + 1) / len(images) * 50)
                self.progress_updated.emit(progress)
            
            if not self.is_cancelled:
                c.save()
                self.operation_completed.emit(True, "تم إنشاء ملف PDF بنجاح")
            else:
                self.operation_completed.emit(False, "تم إلغاء العملية")
                
        except Exception as e:
            self.operation_completed.emit(False, f"خطأ في إنشاء الملف: {str(e)}")
    
    def cancel(self):
        """إلغاء العملية"""
        self.is_cancelled = True

class PDFManager(QObject):
    """مدير ملفات PDF الرئيسي"""
    
    def __init__(self):
        super().__init__()
        self.current_operation = None
        self.mutex = QMutex()
    
    def get_pdf_info(self, file_path: str) -> Dict[str, Any]:
        """الحصول على معلومات ملف PDF"""
        try:
            doc = fitz.open(file_path)
            info = {
                'pages': len(doc),
                'title': doc.metadata.get('title', ''),
                'author': doc.metadata.get('author', ''),
                'subject': doc.metadata.get('subject', ''),
                'creator': doc.metadata.get('creator', ''),
                'producer': doc.metadata.get('producer', ''),
                'creation_date': doc.metadata.get('creationDate', ''),
                'modification_date': doc.metadata.get('modDate', ''),
                'file_size': os.path.getsize(file_path),
                'encrypted': doc.needs_pass
            }
            doc.close()
            return info
        except Exception as e:
            return {'error': str(e)}
    
    def start_operation(self, operation_type: str, **kwargs) -> PDFOperation:
        """بدء عملية PDF جديدة"""
        if self.current_operation and self.current_operation.isRunning():
            self.current_operation.cancel()
            self.current_operation.wait()
        
        self.current_operation = PDFOperation(operation_type, **kwargs)
        return self.current_operation
    
    def cancel_current_operation(self):
        """إلغاء العملية الحالية"""
        if self.current_operation and self.current_operation.isRunning():
            self.current_operation.cancel()
    
    def is_pdf_valid(self, file_path: str) -> bool:
        """التحقق من صحة ملف PDF"""
        try:
            doc = fitz.open(file_path)
            doc.close()
            return True
        except:
            return False

    def extract_text_from_pdf(self, file_path: str) -> List[str]:
        """استخراج النص من ملف PDF"""
        try:
            doc = fitz.open(file_path)
            texts = []
            for page_num in range(len(doc)):
                page = doc[page_num]
                text = page.get_text()
                texts.append(text)
            doc.close()
            return texts
        except Exception as e:
            return [f"خطأ في استخراج النص: {str(e)}"]

    def extract_images_from_pdf(self, file_path: str, output_dir: str) -> List[str]:
        """استخراج الصور من ملف PDF"""
        try:
            doc = fitz.open(file_path)
            image_paths = []

            for page_num in range(len(doc)):
                page = doc[page_num]
                image_list = page.get_images()

                for img_index, img in enumerate(image_list):
                    xref = img[0]
                    pix = fitz.Pixmap(doc, xref)

                    if pix.n - pix.alpha < 4:  # GRAY or RGB
                        img_path = os.path.join(output_dir, f"page_{page_num+1}_img_{img_index+1}.png")
                        pix.save(img_path)
                        image_paths.append(img_path)

                    pix = None

            doc.close()
            return image_paths
        except Exception as e:
            return [f"خطأ في استخراج الصور: {str(e)}"]
