#!/usr/bin/env python3
"""
اختبار سريع لتطبيق PDF Editor Pro
Quick test for PDF Editor Pro application
"""

import sys
import os
from pathlib import Path

# إضافة مسار المشروع
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_imports():
    """اختبار استيراد الوحدات الأساسية"""
    print("🧪 اختبار استيراد الوحدات...")
    print("🧪 Testing module imports...")
    
    try:
        # اختبار PyQt5
        from PyQt5.QtWidgets import QApplication
        print("✅ PyQt5")
        
        # اختبار qtawesome
        import qtawesome as qta
        print("✅ qtawesome")
        
        # اختبار مكتبات PDF
        import fitz  # PyMuPDF
        print("✅ PyMuPDF")
        
        import PyPDF2
        print("✅ PyPDF2")
        
        import pikepdf
        print("✅ pikepdf")
        
        import reportlab
        print("✅ reportlab")
        
        # اختبار مكتبات التحويل
        import docx2pdf
        print("✅ docx2pdf")
        
        import pdf2docx
        print("✅ pdf2docx")
        
        import openpyxl
        print("✅ openpyxl")
        
        from pptx import Presentation
        print("✅ python-pptx")
        
        # اختبار مكتبات الصور
        from PIL import Image
        print("✅ Pillow")
        
        import img2pdf
        print("✅ img2pdf")
        
        import pdf2image
        print("✅ pdf2image")
        
        print("\n✅ جميع المكتبات الأساسية متوفرة!")
        print("✅ All core libraries are available!")
        return True
        
    except ImportError as e:
        print(f"\n❌ خطأ في استيراد المكتبة: {e}")
        print(f"❌ Library import error: {e}")
        return False

def test_config():
    """اختبار وحدات الإعدادات"""
    print("\n🔧 اختبار وحدات الإعدادات...")
    print("🔧 Testing configuration modules...")
    
    try:
        from config.settings import AppSettings, UISettings, ProcessingSettings
        print("✅ config.settings")
        
        from config.themes import ModernTheme, DarkTheme, theme_manager
        print("✅ config.themes")
        
        # اختبار بعض الإعدادات
        assert AppSettings.APP_NAME == "PDF Editor Pro"
        assert UISettings.ANIMATION_DURATION > 0
        assert ProcessingSettings.DEFAULT_DPI > 0
        
        print("✅ جميع الإعدادات صحيحة!")
        print("✅ All settings are valid!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الإعدادات: {e}")
        print(f"❌ Configuration error: {e}")
        return False

def test_core_modules():
    """اختبار الوحدات الأساسية"""
    print("\n⚙️ اختبار الوحدات الأساسية...")
    print("⚙️ Testing core modules...")
    
    try:
        from core.pdf_manager import PDFManager
        print("✅ core.pdf_manager")
        
        from core.converter import FileConverter
        print("✅ core.converter")
        
        from core.editor import PDFEditor
        print("✅ core.editor")
        
        from core.security import SecurityManager
        print("✅ core.security")
        
        # اختبار إنشاء المثيلات
        pdf_manager = PDFManager()
        converter = FileConverter()
        editor = PDFEditor()
        security = SecurityManager()
        
        print("✅ جميع الوحدات الأساسية تعمل!")
        print("✅ All core modules are working!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الوحدات الأساسية: {e}")
        print(f"❌ Core modules error: {e}")
        return False

def test_ui_modules():
    """اختبار وحدات الواجهة"""
    print("\n🖥️ اختبار وحدات الواجهة...")
    print("🖥️ Testing UI modules...")
    
    try:
        from ui.components.sidebar import Sidebar
        print("✅ ui.components.sidebar")
        
        from ui.main_window import MainWindow
        print("✅ ui.main_window")
        
        print("✅ جميع وحدات الواجهة متوفرة!")
        print("✅ All UI modules are available!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في وحدات الواجهة: {e}")
        print(f"❌ UI modules error: {e}")
        return False

def test_directories():
    """اختبار إنشاء المجلدات"""
    print("\n📁 اختبار إنشاء المجلدات...")
    print("📁 Testing directory creation...")
    
    try:
        from config.settings import AppSettings
        AppSettings.ensure_directories()
        
        # التحقق من وجود المجلدات
        directories = [
            AppSettings.TEMP_DIR,
            AppSettings.OUTPUT_DIR,
            AppSettings.RESOURCES_DIR,
            AppSettings.ICONS_DIR,
            AppSettings.STYLES_DIR,
            AppSettings.TRANSLATIONS_DIR
        ]
        
        for directory in directories:
            if directory.exists():
                print(f"✅ {directory.name}")
            else:
                print(f"❌ {directory.name}")
                return False
        
        print("✅ جميع المجلدات تم إنشاؤها!")
        print("✅ All directories created!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء المجلدات: {e}")
        print(f"❌ Directory creation error: {e}")
        return False

def test_app_creation():
    """اختبار إنشاء التطبيق (بدون تشغيل)"""
    print("\n🚀 اختبار إنشاء التطبيق...")
    print("🚀 Testing application creation...")
    
    try:
        # إنشاء QApplication مؤقت للاختبار
        from PyQt5.QtWidgets import QApplication
        import sys
        
        # التحقق من وجود QApplication
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # اختبار إنشاء النافذة الرئيسية
        from ui.main_window import MainWindow
        main_window = MainWindow()
        
        print("✅ تم إنشاء النافذة الرئيسية بنجاح!")
        print("✅ Main window created successfully!")
        
        # تنظيف
        main_window.close()
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء التطبيق: {e}")
        print(f"❌ Application creation error: {e}")
        return False

def main():
    """الدالة الرئيسية للاختبار"""
    print("🧪 PDF Editor Pro - اختبار التطبيق")
    print("🧪 PDF Editor Pro - Application Test")
    print("=" * 50)
    
    tests = [
        ("استيراد المكتبات", "Library Imports", test_imports),
        ("وحدات الإعدادات", "Configuration Modules", test_config),
        ("الوحدات الأساسية", "Core Modules", test_core_modules),
        ("وحدات الواجهة", "UI Modules", test_ui_modules),
        ("إنشاء المجلدات", "Directory Creation", test_directories),
        ("إنشاء التطبيق", "Application Creation", test_app_creation),
    ]
    
    passed = 0
    total = len(tests)
    
    for ar_name, en_name, test_func in tests:
        try:
            if test_func():
                passed += 1
            else:
                print(f"\n❌ فشل اختبار: {ar_name}")
                print(f"❌ Test failed: {en_name}")
        except Exception as e:
            print(f"\n💥 خطأ في اختبار {ar_name}: {e}")
            print(f"💥 Error in test {en_name}: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 نتائج الاختبار: {passed}/{total}")
    print(f"📊 Test Results: {passed}/{total}")
    
    if passed == total:
        print("🎉 جميع الاختبارات نجحت! التطبيق جاهز للتشغيل.")
        print("🎉 All tests passed! Application is ready to run.")
        return True
    else:
        print("⚠️ بعض الاختبارات فشلت. يرجى مراجعة الأخطاء أعلاه.")
        print("⚠️ Some tests failed. Please review the errors above.")
        return False

if __name__ == "__main__":
    success = main()
    
    print("\n" + "=" * 50)
    if success:
        print("✅ يمكنك الآن تشغيل التطبيق باستخدام:")
        print("✅ You can now run the application using:")
        print("   python main.py")
        print("   أو / or")
        print("   python run.py")
    else:
        print("❌ يرجى إصلاح الأخطاء قبل تشغيل التطبيق")
        print("❌ Please fix the errors before running the application")
    
    input("\nاضغط Enter للخروج / Press Enter to exit...")
