"""
محرر محتوى PDF - تحرير النصوص والصور
PDF Content Editor - Edit texts and images
"""

import os
import fitz  # PyMuPDF
from pathlib import Path
from typing import List, Optional, Dict, Any, Tuple
from PyQt5.QtCore import QObject, pyqtSignal, QThread, QRect
from PyQt5.QtGui import QPixmap, QImage
import tempfile
from PIL import Image
import difflib

from config.settings import ProcessingSettings

class EditOperation(QThread):
    """خيط عمليات التحرير"""
    
    progress_updated = pyqtSignal(int)
    operation_completed = pyqtSignal(bool, str)
    status_updated = pyqtSignal(str)
    
    def __init__(self, operation_type, **kwargs):
        super().__init__()
        self.operation_type = operation_type
        self.kwargs = kwargs
        self.is_cancelled = False
    
    def run(self):
        """تشغيل عملية التحرير"""
        try:
            if self.operation_type == "edit_text":
                self.edit_text()
            elif self.operation_type == "add_text":
                self.add_text()
            elif self.operation_type == "remove_text":
                self.remove_text()
            elif self.operation_type == "add_image":
                self.add_image()
            elif self.operation_type == "remove_image":
                self.remove_image()
            elif self.operation_type == "replace_image":
                self.replace_image()
            elif self.operation_type == "repair_pdf":
                self.repair_pdf()
            elif self.operation_type == "compare_pdfs":
                self.compare_pdfs()
            else:
                self.operation_completed.emit(False, "عملية تحرير غير مدعومة")
        except Exception as e:
            self.operation_completed.emit(False, f"خطأ في التحرير: {str(e)}")
    
    def edit_text(self):
        """تحرير النص في PDF"""
        input_file = self.kwargs.get('input_file', '')
        output_file = self.kwargs.get('output_file', '')
        page_number = self.kwargs.get('page_number', 0)
        old_text = self.kwargs.get('old_text', '')
        new_text = self.kwargs.get('new_text', '')
        
        if not all([input_file, output_file, old_text, new_text]):
            self.operation_completed.emit(False, "معلومات تحرير النص غير مكتملة")
            return
        
        self.status_updated.emit("بدء تحرير النص...")
        
        try:
            doc = fitz.open(input_file)
            
            if page_number >= len(doc):
                page_number = len(doc) - 1
            
            page = doc[page_number]
            
            # البحث عن النص واستبداله
            text_instances = page.search_for(old_text)
            
            if not text_instances:
                self.operation_completed.emit(False, "النص المطلوب تحريره غير موجود")
                return
            
            # إزالة النص القديم وإضافة الجديد
            for inst in text_instances:
                # إضافة مستطيل أبيض لإخفاء النص القديم
                page.draw_rect(inst, color=(1, 1, 1), fill=(1, 1, 1))
                
                # إضافة النص الجديد
                page.insert_text(inst.tl, new_text, fontsize=12, color=(0, 0, 0))
            
            self.progress_updated.emit(80)
            
            # حفظ الملف
            doc.save(output_file)
            doc.close()
            
            self.progress_updated.emit(100)
            self.operation_completed.emit(True, f"تم تحرير {len(text_instances)} نص")
            
        except Exception as e:
            self.operation_completed.emit(False, f"خطأ في تحرير النص: {str(e)}")
    
    def add_text(self):
        """إضافة نص جديد"""
        input_file = self.kwargs.get('input_file', '')
        output_file = self.kwargs.get('output_file', '')
        page_number = self.kwargs.get('page_number', 0)
        text = self.kwargs.get('text', '')
        position = self.kwargs.get('position', (100, 100))
        font_size = self.kwargs.get('font_size', 12)
        color = self.kwargs.get('color', (0, 0, 0))
        
        if not all([input_file, output_file, text]):
            self.operation_completed.emit(False, "معلومات إضافة النص غير مكتملة")
            return
        
        self.status_updated.emit("إضافة النص...")
        
        try:
            doc = fitz.open(input_file)
            
            if page_number >= len(doc):
                page_number = len(doc) - 1
            
            page = doc[page_number]
            
            # إضافة النص
            page.insert_text(position, text, fontsize=font_size, color=color)
            
            self.progress_updated.emit(80)
            
            # حفظ الملف
            doc.save(output_file)
            doc.close()
            
            self.progress_updated.emit(100)
            self.operation_completed.emit(True, "تم إضافة النص بنجاح")
            
        except Exception as e:
            self.operation_completed.emit(False, f"خطأ في إضافة النص: {str(e)}")
    
    def add_image(self):
        """إضافة صورة جديدة"""
        input_file = self.kwargs.get('input_file', '')
        output_file = self.kwargs.get('output_file', '')
        page_number = self.kwargs.get('page_number', 0)
        image_path = self.kwargs.get('image_path', '')
        position = self.kwargs.get('position', (100, 100))
        size = self.kwargs.get('size', (200, 200))
        
        if not all([input_file, output_file, image_path]):
            self.operation_completed.emit(False, "معلومات إضافة الصورة غير مكتملة")
            return
        
        if not os.path.exists(image_path):
            self.operation_completed.emit(False, "ملف الصورة غير موجود")
            return
        
        self.status_updated.emit("إضافة الصورة...")
        
        try:
            doc = fitz.open(input_file)
            
            if page_number >= len(doc):
                page_number = len(doc) - 1
            
            page = doc[page_number]
            
            # تحديد موقع وحجم الصورة
            rect = fitz.Rect(position[0], position[1], 
                           position[0] + size[0], position[1] + size[1])
            
            # إدراج الصورة
            page.insert_image(rect, filename=image_path)
            
            self.progress_updated.emit(80)
            
            # حفظ الملف
            doc.save(output_file)
            doc.close()
            
            self.progress_updated.emit(100)
            self.operation_completed.emit(True, "تم إضافة الصورة بنجاح")
            
        except Exception as e:
            self.operation_completed.emit(False, f"خطأ في إضافة الصورة: {str(e)}")
    
    def repair_pdf(self):
        """إصلاح ملف PDF معطوب"""
        input_file = self.kwargs.get('input_file', '')
        output_file = self.kwargs.get('output_file', '')
        
        if not all([input_file, output_file]):
            self.operation_completed.emit(False, "معلومات إصلاح الملف غير مكتملة")
            return
        
        self.status_updated.emit("بدء إصلاح الملف...")
        
        try:
            # محاولة فتح الملف وإصلاحه
            doc = fitz.open(input_file)
            
            # فحص كل صفحة
            total_pages = len(doc)
            repaired_pages = 0
            
            for page_num in range(total_pages):
                if self.is_cancelled:
                    break
                
                try:
                    page = doc[page_num]
                    # محاولة تنظيف محتوى الصفحة
                    page.clean_contents()
                    repaired_pages += 1
                except:
                    # تخطي الصفحات المعطوبة
                    pass
                
                progress = int((page_num + 1) / total_pages * 100)
                self.progress_updated.emit(progress)
                self.status_updated.emit(f"إصلاح الصفحة {page_num + 1} من {total_pages}")
            
            if not self.is_cancelled:
                # حفظ الملف المُصلح
                doc.save(output_file, garbage=4, deflate=True, clean=True)
                doc.close()
                
                self.operation_completed.emit(True, 
                    f"تم إصلاح الملف. تم إصلاح {repaired_pages} من {total_pages} صفحة")
            else:
                doc.close()
                self.operation_completed.emit(False, "تم إلغاء العملية")
                
        except Exception as e:
            self.operation_completed.emit(False, f"خطأ في إصلاح الملف: {str(e)}")
    
    def compare_pdfs(self):
        """مقارنة ملفي PDF"""
        file1 = self.kwargs.get('file1', '')
        file2 = self.kwargs.get('file2', '')
        output_file = self.kwargs.get('output_file', '')
        
        if not all([file1, file2]):
            self.operation_completed.emit(False, "ملفات المقارنة غير محددة")
            return
        
        self.status_updated.emit("بدء مقارنة الملفات...")
        
        try:
            doc1 = fitz.open(file1)
            doc2 = fitz.open(file2)
            
            # مقارنة عدد الصفحات
            pages1 = len(doc1)
            pages2 = len(doc2)
            max_pages = max(pages1, pages2)
            
            differences = []
            
            for page_num in range(max_pages):
                if self.is_cancelled:
                    break
                
                # استخراج النص من كلا الملفين
                text1 = ""
                text2 = ""
                
                if page_num < pages1:
                    text1 = doc1[page_num].get_text()
                
                if page_num < pages2:
                    text2 = doc2[page_num].get_text()
                
                # مقارنة النصوص
                if text1 != text2:
                    diff = list(difflib.unified_diff(
                        text1.splitlines(keepends=True),
                        text2.splitlines(keepends=True),
                        fromfile=f"الملف الأول - صفحة {page_num + 1}",
                        tofile=f"الملف الثاني - صفحة {page_num + 1}",
                        lineterm=""
                    ))
                    
                    if diff:
                        differences.extend(diff)
                
                progress = int((page_num + 1) / max_pages * 100)
                self.progress_updated.emit(progress)
                self.status_updated.emit(f"مقارنة الصفحة {page_num + 1} من {max_pages}")
            
            doc1.close()
            doc2.close()
            
            if not self.is_cancelled:
                # حفظ نتائج المقارنة
                if output_file and differences:
                    with open(output_file, 'w', encoding='utf-8') as f:
                        f.write('\n'.join(differences))
                
                diff_count = len([d for d in differences if d.startswith('+')])
                self.operation_completed.emit(True, 
                    f"تمت المقارنة. تم العثور على {diff_count} اختلاف")
            else:
                self.operation_completed.emit(False, "تم إلغاء العملية")
                
        except Exception as e:
            self.operation_completed.emit(False, f"خطأ في مقارنة الملفات: {str(e)}")
    
    def cancel(self):
        """إلغاء العملية"""
        self.is_cancelled = True

class PDFEditor(QObject):
    """محرر PDF الرئيسي"""
    
    def __init__(self):
        super().__init__()
        self.current_operation = None
    
    def start_edit_operation(self, operation_type: str, **kwargs) -> EditOperation:
        """بدء عملية تحرير جديدة"""
        if self.current_operation and self.current_operation.isRunning():
            self.current_operation.cancel()
            self.current_operation.wait()
        
        self.current_operation = EditOperation(operation_type, **kwargs)
        return self.current_operation
    
    def cancel_current_operation(self):
        """إلغاء العملية الحالية"""
        if self.current_operation and self.current_operation.isRunning():
            self.current_operation.cancel()
    
    def get_text_from_page(self, file_path: str, page_number: int) -> str:
        """استخراج النص من صفحة محددة"""
        try:
            doc = fitz.open(file_path)
            if page_number < len(doc):
                text = doc[page_number].get_text()
                doc.close()
                return text
            doc.close()
            return ""
        except Exception as e:
            return f"خطأ في استخراج النص: {str(e)}"
    
    def get_images_from_page(self, file_path: str, page_number: int) -> List[Dict]:
        """استخراج معلومات الصور من صفحة محددة"""
        try:
            doc = fitz.open(file_path)
            images_info = []
            
            if page_number < len(doc):
                page = doc[page_number]
                image_list = page.get_images()
                
                for img_index, img in enumerate(image_list):
                    xref = img[0]
                    bbox = page.get_image_bbox(img)
                    
                    images_info.append({
                        'index': img_index,
                        'xref': xref,
                        'bbox': bbox,
                        'width': img[2],
                        'height': img[3]
                    })
            
            doc.close()
            return images_info
            
        except Exception as e:
            return [{'error': str(e)}]
    
    def search_text_in_pdf(self, file_path: str, search_text: str) -> List[Dict]:
        """البحث عن نص في PDF"""
        try:
            doc = fitz.open(file_path)
            results = []
            
            for page_num in range(len(doc)):
                page = doc[page_num]
                text_instances = page.search_for(search_text)
                
                for inst in text_instances:
                    results.append({
                        'page': page_num + 1,
                        'bbox': inst,
                        'text': search_text
                    })
            
            doc.close()
            return results
            
        except Exception as e:
            return [{'error': str(e)}]
